from flask import Flask, request, render_template, send_file, session, redirect, url_for, flash
import time
import json
import subprocess
import sys
import threading
import os
import hashlib
from functools import wraps
from datetime import datetime, timedelta
import pandas as pd

app = Flask(__name__)
app.secret_key = 'autospace_secret_key_2024'  # Change this to a secure random key in production

sessions_dict = {}  # Will be dynamically populated with user IDs
tools_dict = {}

# User ID mapping - stores email to user_id mapping
user_id_mapping = {}

# Ensure required directories exist
os.makedirs("temp_input_files", exist_ok=True)
os.makedirs("output_files", exist_ok=True)
os.makedirs("my_databases", exist_ok=True)

def generate_user_id(email):
    """Generate a unique user ID based on email"""
    # Create a hash of the email for consistency
    email_hash = hashlib.md5(email.encode()).hexdigest()[:8]
    user_id = f"U{email_hash.upper()}"
    return user_id

def get_or_create_user_id(email):
    """Get existing user ID or create new one for email"""
    global user_id_mapping, sessions_dict

    if email in user_id_mapping:
        return user_id_mapping[email]

    # Generate new user ID
    user_id = generate_user_id(email)
    user_id_mapping[email] = user_id

    # Initialize sessions dict for this user
    if user_id not in sessions_dict:
        sessions_dict[user_id] = {}

    return user_id

# User authentication functions
def load_users():
    """Load users from the text file"""
    users = {}
    try:
        with open('users.txt', 'r') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#'):
                    parts = line.split(':')
                    if len(parts) >= 3:
                        email, username, password = parts[:3]
                        users[email] = {
                            'username': username,
                            'password': password,
                            'email': email
                        }
    except FileNotFoundError:
        print("Warning: users.txt file not found.")
    return users

def load_whitelist():
    """Load whitelist from the text file"""
    whitelist = {}
    try:
        with open('whitelist.txt', 'r') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#'):
                    parts = line.split(':')
                    if len(parts) >= 2:
                        email, role = parts[:2]
                        whitelist[email] = role
    except FileNotFoundError:
        print("Warning: whitelist.txt file not found.")
    return whitelist

def is_email_whitelisted(email):
    """Check if email is in whitelist"""
    whitelist = load_whitelist()
    return email in whitelist

def get_user_role(email):
    """Get user role from whitelist"""
    whitelist = load_whitelist()
    return whitelist.get(email, 'user')

def authenticate_user(email, password):
    """Authenticate user credentials"""
    users = load_users()
    if email in users and users[email]['password'] == password:
        user_info = users[email].copy()
        user_info['role'] = get_user_role(email)
        return user_info
    return None

def register_user(email, username, password):
    """Register a new user"""
    # Check if email is whitelisted
    if not is_email_whitelisted(email):
        return False, "Email not authorized. Contact administrator."

    # Check if user already exists
    users = load_users()
    if email in users:
        return False, "Account already exists with this email."

    # Check if username is taken
    for user_data in users.values():
        if user_data['username'] == username:
            return False, "Username already taken."

    # Add user to database
    try:
        with open('users.txt', 'a') as f:
            f.write(f"{email}:{username}:{password}\n")
        return True, "Account created successfully!"
    except Exception as e:
        return False, f"Error creating account: {str(e)}"

def login_required(f):
    """Decorator to require login for routes"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'user_email' not in session:
            return redirect(url_for('login'))
        return f(*args, **kwargs)
    return decorated_function

def get_current_user():
    """Get current logged-in user information"""
    if 'user_email' in session:
        users = load_users()
        email = session['user_email']
        if email in users:
            user_info = users[email].copy()
            user_info['role'] = get_user_role(email)
            user_info['user_id'] = get_or_create_user_id(email)
            return user_info
    return None

def track_progress(user_id, session_id, process):
    global sessions_dict
    try:
        for current_progress in process.stdout:
            current_progress = current_progress.strip()
            if not current_progress.isdigit():
                continue

            # Check if session still exists and is not killed
            if (user_id not in sessions_dict or
                session_id not in sessions_dict[user_id] or
                sessions_dict[user_id][session_id]["status"] == "Killed"):
                break

            sessions_dict[user_id][session_id]["progress"] = int(current_progress)

        process.wait()

        # Check if session still exists before marking as done
        if (user_id in sessions_dict and
            session_id in sessions_dict[user_id] and
            sessions_dict[user_id][session_id]["status"] != "Killed"):

            sessions_dict[user_id][session_id]["status"] = "Done"

            # Get tool name safely
            tool_id = sessions_dict[user_id][session_id]['tool_id']
            tool_name = "Unknown Tool"
            if tool_id in tools_dict and len(tools_dict[tool_id]) > 0:
                tool_name = tools_dict[tool_id][0].get('Name', f'Tool {tool_id}')

            with open("my_databases/finished_sessions.txt", "a") as f:
                f.write(f"{user_id}\t{session_id}\t{time.strftime('%H:%M')}\t{tool_name}\n")

    except (KeyError, IndexError, TypeError) as e:
        print(f"Error in track_progress: {e}")  # Log error for debugging
        pass  # Session deleted while tracking or other error


# Authentication routes
@app.route("/register", methods=['GET', 'POST'])
def register():
    if request.method == 'POST':
        email = request.form.get('email', '').strip().lower()
        username = request.form.get('username', '').strip()
        password = request.form.get('password', '')
        confirm_password = request.form.get('confirm_password', '')
        terms = request.form.get('terms') == 'on'

        # Validation
        if not email or not username or not password:
            flash('All fields are required', 'error')
            return render_template('register.html')

        if not email.endswith('@siliconexpert.com'):
            flash('Only @siliconexpert.com email addresses are allowed', 'error')
            return render_template('register.html')

        if password != confirm_password:
            flash('Passwords do not match', 'error')
            return render_template('register.html')

        if len(password) < 8:
            flash('Password must be at least 8 characters long', 'error')
            return render_template('register.html')

        if not terms:
            flash('You must agree to the Terms of Service', 'error')
            return render_template('register.html')

        # Register user
        success, message = register_user(email, username, password)
        if success:
            flash(message, 'success')
            return redirect(url_for('login'))
        else:
            flash(message, 'error')

    return render_template('register.html')

@app.route("/login", methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        email = request.form.get('email', '').strip().lower()
        password = request.form.get('password', '')

        if not email or not password:
            flash('Please enter both email and password', 'error')
            return render_template('login.html')

        user = authenticate_user(email, password)
        if user:
            user_id = get_or_create_user_id(email)
            session['user_email'] = email
            session['user_role'] = user['role']
            session['user_name'] = user['username']
            session['user_id'] = user_id
            session['login_time'] = datetime.now().isoformat()

            # Set session timeout (8 hours)
            session.permanent = True
            app.permanent_session_lifetime = timedelta(hours=8)

            flash(f'Welcome back, {user["username"]}!', 'success')

            # Redirect to the page they were trying to access, or home
            next_page = request.args.get('next')
            if next_page:
                return redirect(next_page)
            return redirect(url_for('home'))
        else:
            flash('Invalid email or password', 'error')

    return render_template('login.html')

@app.route("/logout")
def logout():
    user_name = session.get('user_name', 'User')
    session.clear()
    flash(f'Goodbye, {user_name}! You have been logged out.', 'success')
    return redirect(url_for('login'))

@app.route("/")
@login_required
def home():
    current_user = get_current_user()
    return render_template("main.html", current_user=current_user)

@app.get("/tools-data")
@login_required
def get_tools_data():
    global tools_dict

    df = pd.read_excel("my_databases/tools_data.xlsx")

    tools_dict = {}
    for _, row in df.iterrows():
        tool_id = row['ID']
        if tool_id not in tools_dict:
            tools_dict[tool_id] = []
        
        tool_info = {
            'Name': row['Name'],
            'Description': row['Description'],
            'Tool_Name': row['Tool_Name'],
            "Category": row['Category'],
            'Category_ID': row['Category_ID']
        }
        tools_dict[tool_id].append(tool_info)

    return json.dumps(tools_dict)

@app.get("/sessions-all/<string:user_id>")
@login_required
def get_all_sessions(user_id):
    global sessions_dict

    # Security check: ensure user can only access their own sessions
    current_user = get_current_user()
    if not current_user or current_user['user_id'] != user_id:
        return {"error": "Unauthorized access"}, 403

    user_running_sesseions_dict = {}
    user_finished_sessions = []
    sessions_to_remove = []

    if user_id not in sessions_dict:
        sessions_dict[user_id] = {}

    try:
        with open("my_databases/finished_sessions.txt", "r") as sessions_log_file:
            all_finished_sessions = sessions_log_file.readlines()
            all_finished_sessions = all_finished_sessions[::-1]
            i = 1
            for finished_session in all_finished_sessions:
                if i > 5:
                    break
                if user_id in finished_session:
                    i += 1
                    parts = finished_session.split("\t")
                    if len(parts) >= 4:  # Ensure we have all required parts
                        user_finished_sessions.append([parts[1].strip(), parts[2].strip(), parts[-1].strip()])
    except FileNotFoundError:
        # Create empty file if it doesn't exist
        with open("my_databases/finished_sessions.txt", "w") as f:
            pass
    except Exception as e:
        print(f"Error reading finished sessions: {e}")

    for running_session, session_data in sessions_dict[user_id].items():
        if session_data["status"] in ["Done", "Killed"]:
            sessions_to_remove.append(running_session)
            continue
        
        user_running_sesseions_dict[running_session] = {"progress": session_data["progress"],"status": session_data["status"], "timestamp":session_data["timestamp"], "tool_id":session_data["tool_id"]}

    for session in sessions_to_remove:
        del sessions_dict[user_id][session]

    return {"Running":user_running_sesseions_dict, "Done":user_finished_sessions}



@app.route("/session-run/<string:session_id>-<string:user_id>", methods=['POST'])
@login_required
def run(session_id, user_id):
    global sessions_dict
    global tools_dict

    # Security check: ensure user can only run sessions for themselves
    current_user = get_current_user()
    if not current_user or current_user['user_id'] != user_id:
        return {"error": "Unauthorized access"}, 403


    input_file = request.files['file']
    input_file_path = f"temp_input_files\\input_{session_id}_{user_id}.txt"
    output_file_path = f"output_files\\output_{session_id}_{user_id}.txt"
    input_file.save(input_file_path)

    tool_id = request.form['tool_id']
    mode = request.form['mode']

    tool_path = f'python_tools\\{tools_dict[tool_id][0]["Tool_Name"]}.py'
    
    with open(output_file_path, "w") as output_file:
        pass

    session_id = str(session_id)
    user_id = str(user_id)

    # Ensure user_id exists in sessions_dict
    if user_id not in sessions_dict:
        sessions_dict[user_id] = {}

    process = subprocess.Popen(
        [sys.executable,"-u", tool_path, input_file_path, output_file_path, mode],
        stdout=subprocess.PIPE,   # Capture stdout, stderr to same pipe
        stderr=subprocess.STDOUT,   # Capture stderr
        text=True                  # Decode to string instead of bytes
    )


    sessions_dict[user_id][session_id] = {"progress": 0, "process": process, "status":"Running", "timestamp":time.strftime("%H:%M"), "output_path": output_file_path, "tool_id":tool_id}
    
    threading.Thread(target=track_progress, args=(user_id, session_id, process), daemon=True).start()

    return {"message": f"Session {session_id} Running"}




@app.get("/session-refresh/<string:session_id>-<string:user_id>")
@login_required
def update(session_id, user_id):
    global sessions_dict

    # Security check: ensure user can only refresh their own sessions
    current_user = get_current_user()
    if not current_user or current_user['user_id'] != user_id:
        return {"error": "Unauthorized access"}, 403

    # Check if user and session exist
    if (user_id in sessions_dict and
        session_id in sessions_dict[user_id] and
        sessions_dict[user_id][session_id]["status"] in ["Running", "Killed"]):
        return {
            "progress": sessions_dict[user_id][session_id]["progress"],
            "status": sessions_dict[user_id][session_id]["status"]
        }

    return {"progress": 100, "status": "Done"}

@app.get("/session-kill/<string:session_id>-<string:user_id>")
@login_required
def kill(session_id, user_id):
    # Security check: ensure user can only kill their own sessions
    current_user = get_current_user()
    if not current_user or current_user['user_id'] != user_id:
        return {"error": "Unauthorized access"}, 403

    if user_id in sessions_dict and session_id in sessions_dict[user_id]:
        proc = sessions_dict[user_id][session_id]["process"]
        proc.terminate()
        sessions_dict[user_id][session_id]["status"] = "Killed"
        return {"message": f"Session {session_id} Killed"}
    return {"message": "Session not found"}

@app.get("/session-download/<string:session_id>-<string:user_id>")
@login_required
def session_download(session_id, user_id):
    # Security check: ensure user can only download their own session files
    current_user = get_current_user()
    if not current_user or current_user['user_id'] != user_id:
        return {"error": "Unauthorized access"}, 403

    output_path = f"output_files\\output_{session_id}_{user_id}.txt"
    try:
        return send_file(
            output_path,
            as_attachment=True,
            download_name=f"output_{session_id}_{user_id}.txt",
            mimetype="text/plain"
        )
    except FileNotFoundError:
        return {"message": "File not found"}, 410

if __name__ == '__main__':
    app.run(debug=True)