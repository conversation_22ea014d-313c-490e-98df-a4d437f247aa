/* ========================================
   AUTOSPACE CSS - ORGANIZED BY HTML STRUCTURE
   ======================================== */

@import url('https://fonts.googleapis.com/css2?family=Nata+Sans:wght@100..900&display=swap');

/* ========================================
   CSS VARIABLES & ROOT STYLES
   ======================================== */

:root {
  /* Typography Variables */
  --font-family-primary: "nata-sans", 'montserrat';
  --font-size-title-large: clamp(2.5rem, 5vw, 3rem);
  --font-size-title-medium: clamp(1.25rem, 3vw, 1.5rem);
  --font-size-title-small: clamp(1.1rem, 2.5vw, 1.2rem);
  --font-size-regular: clamp(0.9rem, 2vw, 1rem);
  --font-size-small: clamp(0.8rem, 1.8vw, 0.875rem);
  --font-size-icon-large: clamp(32px, 6vw, 40px);
  --font-size-icon-medium: clamp(20px, 4vw, 24px);
  --font-size-icon-small: clamp(16px, 3vw, 20px);

  /* Font Weights */
  --font-weight-light: 100;
  --font-weight-regular: 300;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;

  /* Modern Minimalist Color Palette - Enhanced Professional Design */
  --color-primary-accent: #2563eb;
  --color-primary-accent-hover: #1d4ed8;
  --color-primary-accent-dark: #1e40af;
  --color-primary-accent-light: #3b82f6;
  --color-secondary-accent: #64748b;
  --color-secondary-accent-hover: #475569;
  --color-text-primary: #0f172a;
  --color-text-secondary: #334155;
  --color-text-muted: #64748b;
  --color-text-inverse: #ffffff;
  --color-text-on-accent: #ffffff;
  --color-background-main: #f8fafc;
  --color-background-card: #ffffff;
  --color-background-secondary: #f1f5f9;
  --color-background-overlay: rgba(15, 23, 42, 0.8);
  --color-background-hover: rgba(37, 99, 235, 0.04);
  --color-background-focus: rgba(37, 99, 235, 0.08);
  --color-background-active: rgba(37, 99, 235, 0.12);
  --color-border-primary: #e2e8f0;
  --color-border-secondary: #cbd5e1;
  --color-border-accent: var(--color-primary-accent);
  --color-border-focus: var(--color-primary-accent);
  --color-success: #059669;
  --color-success-light: #10b981;
  --color-warning: #d97706;
  --color-warning-light: #f59e0b;
  --color-error: #dc2626;
  --color-error-light: #ef4444;

  /* Spacing Variables */
  --spacing-minimal: 1px;
  --spacing-tiny: 4px;
  --spacing-small: 8px;
  --spacing-medium: 12px;
  --spacing-large: 20px;
  --spacing-extra-large: 25px;
  --spacing-huge: 40px;

  /* Layout Variables */
  --sidebar-width: 70px;
  --sidebar-width-mobile: 60px;
  --card-max-height: 90px;
  --tools-section-max-width: 300px;
  --content-max-width: 1200px;
  --border-width-thin: 1px;
  --border-width-medium: 2px;
  --border-width-thick: 3px;

  /* Modern Minimalist Shadow System with Enhanced Depth */
  --shadow-subtle: 0 1px 3px rgba(15, 23, 42, 0.06), 0 1px 2px rgba(15, 23, 42, 0.03);
  --shadow-soft: 0 2px 6px rgba(15, 23, 42, 0.08), 0 1px 3px rgba(15, 23, 42, 0.04);
  --shadow-medium: 0 4px 12px rgba(15, 23, 42, 0.10), 0 2px 6px rgba(15, 23, 42, 0.05);
  --shadow-strong: 0 8px 24px rgba(15, 23, 42, 0.12), 0 4px 12px rgba(15, 23, 42, 0.06);
  --shadow-elevated: 0 16px 32px rgba(15, 23, 42, 0.15), 0 8px 16px rgba(15, 23, 42, 0.08);
  --shadow-focus: 0 0 0 3px rgba(37, 99, 235, 0.15);
  --shadow-focus-error: 0 0 0 3px rgba(220, 38, 38, 0.15);
  --shadow-focus-success: 0 0 0 3px rgba(5, 150, 105, 0.15);
  --shadow-inset: inset 0 2px 4px rgba(15, 23, 42, 0.04);
  --shadow-glow: 0 0 24px rgba(37, 99, 235, 0.12);

  /* Enhanced Transition System with Performance Optimization */
  --transition-instant: 0.1s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-fast: 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-smooth: 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-slow: 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-bounce: 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);
  --transition-elastic: 0.6s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  --transition-ease-out: 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  --transition-ease-in: 0.3s cubic-bezier(0.55, 0.055, 0.675, 0.19);

  /* Standardized Interactive States for Consistent UX */
  --hover-scale-subtle: 1.02;
  --hover-scale-medium: 1.05;
  --hover-translate-up: -3px;
  --hover-translate-up-medium: -5px;
  --focus-outline-width: 2px;
  --focus-outline-offset: 2px;
  --active-scale: 0.96;

  /* Unified Button States */
  --button-hover-transform: translateY(-3px) scale(1.02);
  --button-active-transform: translateY(-1px) scale(0.96);
  --button-focus-border: var(--color-primary-accent);

  /* Unified Card States */
  --card-hover-transform: scale(1.02) translateY(-3px);
  --card-active-transform: scale(0.96);
  --card-hover-shadow: var(--shadow-medium);

  /* Unified Input States */
  --input-hover-transform: scale(1.02);
  --input-active-transform: scale(0.96);
  --input-focus-border: var(--color-primary-accent);

  /* Scrollbar Styling Variables */
  --scrollbar-width: 8px;
  --scrollbar-track-color: var(--color-background-main);
  --scrollbar-thumb-color: var(--color-border-secondary);
  --scrollbar-thumb-hover-color: var(--color-primary-accent);

  /* Glass Effect Variables */
  --glass-blur-light: blur(10px);
  --glass-blur-medium: blur(15px);
  --glass-bg-primary: rgba(35, 36, 40, 0.7);
  --glass-bg-secondary: rgba(36, 36, 37, 0.6);

  /* Enhanced Animation System */
  --animation-duration-instant: 0.1s;
  --animation-duration-fast: 0.2s;
  --animation-duration-medium: 0.4s;
  --animation-duration-slow: 0.6s;
  --animation-duration-extra-slow: 0.8s;
  --animation-ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
  --animation-ease-bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55);
  --animation-ease-spring: cubic-bezier(0.175, 0.885, 0.32, 1.275);
  --animation-ease-smooth: cubic-bezier(0.25, 0.46, 0.45, 0.94);

  /* Z-index Management System */
  --z-index-base: 1;
  --z-index-dropdown: 1000;
  --z-index-sticky: 1020;
  --z-index-modal: 1050;
  --z-index-tooltip: 1100;
  --z-index-overlay: 1200;
}

/* ========================================
   KEYFRAME ANIMATIONS FOR ENTRANCE EFFECTS
   ======================================== */

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-3px);
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes glow {
  0%, 100% {
    box-shadow: 0 0 5px var(--color-primary-accent);
  }
  50% {
    box-shadow: 0 0 20px var(--color-primary-accent), 0 0 30px var(--color-primary-accent);
  }
}

@keyframes heartbeat {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}

@keyframes slideInFromTop {
  from {
    opacity: 0;
    transform: translateY(-30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* ========================================
   ANIMATION UTILITY CLASSES
   ======================================== */

.animate-fade-in {
  animation: fadeIn var(--animation-duration-medium) var(--animation-ease-smooth) forwards;
}

.animate-slide-in-left {
  animation: slideInLeft var(--animation-duration-medium) var(--animation-ease-smooth) forwards;
}

.animate-slide-in-right {
  animation: slideInRight var(--animation-duration-medium) var(--animation-ease-smooth) forwards;
}

.animate-slide-in-up {
  animation: slideInUp var(--animation-duration-medium) var(--animation-ease-smooth) forwards;
}

.animate-scale-in {
  animation: scaleIn var(--animation-duration-fast) var(--animation-ease-bounce) forwards;
}

.animate-pulse {
  animation: pulse 2s infinite;
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

.animate-spin {
  animation: spin 1s linear infinite;
}

.animate-glow {
  animation: glow 2s ease-in-out infinite;
}

.animate-heartbeat {
  animation: heartbeat 1.5s ease-in-out infinite;
}

.animate-slide-in-top {
  animation: slideInFromTop var(--animation-duration-medium) var(--animation-ease-smooth) forwards;
}

/* Loading states */
.loading {
  position: relative;
  overflow: hidden;
}

.loading::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  animation: shimmer 1.5s infinite;
}

/* Staggered animation delays for sequential entrance effects */
.animate-delay-1 { animation-delay: 0.1s; }
.animate-delay-2 { animation-delay: 0.2s; }
.animate-delay-3 { animation-delay: 0.3s; }
.animate-delay-4 { animation-delay: 0.4s; }
.animate-delay-5 { animation-delay: 0.5s; }
.animate-delay-6 { animation-delay: 0.6s; }
.animate-delay-7 { animation-delay: 0.7s; }
.animate-delay-8 { animation-delay: 0.8s; }

/* ========================================
   MODERN UTILITY CLASSES FOR ENHANCED UX
   ======================================== */

/* Interactive states */
.interactive {
  cursor: pointer;
  transition: var(--transition-smooth);
  user-select: none;
  -webkit-tap-highlight-color: transparent;
}

.interactive:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-medium);
}

.interactive:active {
  transform: translateY(0);
  box-shadow: var(--shadow-subtle);
}

/* Glass morphism effect */
.glass {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Gradient backgrounds */
.gradient-primary {
  background: linear-gradient(135deg, var(--color-primary-accent), var(--color-primary-accent-light));
}

.gradient-secondary {
  background: linear-gradient(135deg, var(--color-secondary-accent), var(--color-secondary-accent-hover));
}

/* Modern button variants */
.btn-modern {
  padding: var(--spacing-medium) var(--spacing-large);
  border: none;
  font-weight: var(--font-weight-medium);
  transition: var(--transition-smooth);
  cursor: pointer;
  position: relative;
  overflow: hidden;
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-small);
  text-decoration: none;
  font-family: inherit;
}

.btn-modern::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: var(--transition-smooth);
}

.btn-modern:hover::before {
  left: 100%;
}

/* Card enhancements */
.card-modern {
  background: var(--color-background-card);
  border: 1px solid var(--color-border-primary);
  box-shadow: var(--shadow-subtle);
  transition: var(--transition-smooth);
  overflow: hidden;
}

.card-modern:hover {
  box-shadow: var(--shadow-medium);
  transform: translateY(-2px);
}

/* Focus management */
.focus-ring:focus {
  outline: none;
  border-color: var(--color-primary-accent);
}

.focus-ring:focus:not(:focus-visible) {
  border-color: inherit;
}

.focus-ring:focus-visible {
  border-color: var(--color-primary-accent);
}

/* ========================================
   GLOBAL RESET & BASE STYLES
   ======================================== */

/* Reset and Base Styles with Enhanced Cross-browser Support */
*,
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

/* ========================================
   HTML ELEMENT
   ======================================== */

html {
  font-size: 16px;
  scroll-behavior: smooth;
  -webkit-text-size-adjust: 100%;
  -ms-text-size-adjust: 100%;
}

/* ========================================
   BODY ELEMENT
   ======================================== */

body {
  font-family: var(--font-family-primary);
  background-color: var(--color-background-main);
  color: var(--color-text-primary);
  margin: 0;
  display: flex;
  flex-direction: row;
  min-height: 100vh;
  overflow-x: hidden;
  font-weight: var(--font-weight-light);
  font-size: var(--font-size-regular);
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
  /* Modern canvas-style dotted grid background */
  background-color: var(--color-background-main);
  background-image:
    radial-gradient(circle at 1px 1px, var(--color-border-secondary) 1px, transparent 0);
  background-size: 20px 20px;
  background-position: 0 0;
  /* Entrance animation for the entire page */
  animation: fadeIn var(--animation-duration-slow) var(--animation-ease-smooth);
}

/* Enhanced Focus Management for Accessibility - Clean Borders */
*:focus {
  outline: none;
  border-color: var(--color-primary-accent);
}

*:focus:not(:focus-visible) {
  outline: none;
  border-color: inherit;
}

*:focus-visible {
  outline: none;
  border-color: var(--color-primary-accent);
  box-shadow: var(--shadow-focus);
}

/* Enhanced Interactive States */
button:focus-visible,
.interactive:focus-visible {
  box-shadow: var(--shadow-focus);
  border-color: var(--color-primary-accent);
}

/* Improved Cursor Behaviors */
.interactive,
button,
[role="button"],
input[type="checkbox"],
input[type="file"],
label {
  cursor: pointer;
}

button:disabled,
.interactive:disabled {
  cursor: not-allowed;
  opacity: 0.6;
}

/* ========================================
   CUSTOM SCROLLBAR STYLES
   ======================================== */

/* Webkit Scrollbars (Chrome, Safari, Edge) */
::-webkit-scrollbar {
  width: var(--scrollbar-width);
  height: var(--scrollbar-width);
}

::-webkit-scrollbar-track {
  background: var(--scrollbar-track-color);
  border-radius: 0;
}

::-webkit-scrollbar-thumb {
  background: var(--scrollbar-thumb-color);
  border-radius: 0;
  transition: var(--transition-smooth);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--scrollbar-thumb-hover-color);
}

::-webkit-scrollbar-corner {
  background: var(--scrollbar-track-color);
}

/* Firefox Scrollbars */
* {
  scrollbar-width: thin;
  scrollbar-color: var(--scrollbar-thumb-color) var(--scrollbar-track-color);
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* ========================================
   SIDE-BAR-CONTAINER ELEMENT
   ======================================== */

.side-bar-container {
  color: var(--color-text-primary);
  background-color: var(--color-background-card);
  display: flex;
  flex-direction: column;
  height: 100vh;
  width: var(--sidebar-width);
  align-items: center;
  padding: var(--spacing-large) 0;
  position: sticky;
  top: 0;
  z-index: var(--z-index-sticky);
  border-right: var(--border-width-thin) solid var(--color-border-primary);
  box-shadow: var(--shadow-medium);
  backdrop-filter: blur(12px);
  transition: var(--transition-smooth);
  /* Entrance animation */
  animation: slideInLeft var(--animation-duration-medium) var(--animation-ease-smooth);
}

/* ========================================
   LOGO ELEMENT (inside side-bar-container)
   ======================================== */

.side-bar-container .logo {
  color: var(--color-primary-accent);
  font-size: var(--font-size-title-large);
  margin-bottom: var(--spacing-large);
  font-weight: var(--font-weight-bold);
  transition: var(--transition-smooth);
  cursor: pointer;
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  position: relative;
  /* Entrance animation with delay */
  animation: scaleIn var(--animation-duration-medium) var(--animation-ease-bounce) 0.2s both;
}

.side-bar-container .logo::after {
  content: '';
  position: absolute;
  bottom: -4px;
  left: 50%;
  width: 0;
  height: 2px;
  background: linear-gradient(90deg, var(--color-primary-accent), var(--color-primary-accent-light));
  transition: var(--transition-smooth);
  transform: translateX(-50%);
}

.side-bar-container .logo:hover {
  color: var(--color-primary-accent-hover);
  transform: scale(var(--hover-scale-subtle)) translateY(-2px);
  text-shadow: 0 2px 4px rgba(8, 25, 248, 0.2);
}

.side-bar-container .logo:hover::after {
  width: 100%;
}

.side-bar-container .logo:focus {
  outline: none;
  color: var(--color-primary-accent-hover);
}

.side-bar-container .logo:active {
  transform: scale(var(--active-scale));
}

/* ========================================
   SIDE-BAR ELEMENT
   ======================================== */

.side-bar {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  justify-self: center;
  gap: var(--spacing-small);
  flex-grow: 1;
}

/* ========================================
   TOOL-CATEGORY ELEMENTS
   ======================================== */

.tool-category {
  color: var(--color-text-secondary);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-medium);
  cursor: pointer;
  position: relative;
  overflow: hidden;
  font-size: var(--font-size-icon-large);
  font-weight: var(--font-weight-regular);
  transition: var(--transition-smooth);
  border-left: var(--border-width-medium) solid transparent;
  min-width: 48px;
  min-height: 48px;
  margin: var(--spacing-tiny) 0;
  background-color: transparent;
  /* Staggered entrance animation */
  animation: slideInLeft var(--animation-duration-medium) var(--animation-ease-smooth) both;
}

.tool-category:nth-child(1) { animation-delay: 0.3s; }
.tool-category:nth-child(2) { animation-delay: 0.4s; }
.tool-category:nth-child(3) { animation-delay: 0.5s; }
.tool-category:nth-child(4) { animation-delay: 0.6s; }
.tool-category:nth-child(5) { animation-delay: 0.7s; }

.tool-category::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, var(--color-background-hover), transparent);
  opacity: 0;
  transition: var(--transition-fast);
  z-index: -1;
}

.tool-category:hover::before {
  opacity: 1;
}

.tool-category:focus,
.tool-category:hover {
  font-weight: var(--font-weight-bold);
  border-left: var(--border-width-medium) solid var(--color-primary-accent);
  color: var(--color-primary-accent);
  background-color: var(--color-background-hover);
  transform: translateX(var(--spacing-tiny)) scale(var(--hover-scale-subtle));
  box-shadow: var(--shadow-subtle);
}

.tool-category:active {
  transform: translateX(var(--spacing-tiny)) scale(var(--active-scale));
}

/* ========================================
   MAIN-CONTENT ELEMENT
   ======================================== */

.main-content {
  display: flex;
  flex-direction: row;
  flex-grow: 1;
  padding: var(--spacing-huge);
  gap: var(--spacing-huge);
  /* Entrance animation */
  animation: slideInRight var(--animation-duration-medium) var(--animation-ease-smooth) 0.1s both;
}

/* ========================================
   TOOLS-CARDS-SECTION ELEMENT
   ======================================== */

.tools-cards-section {
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  padding: var(--spacing-small);
  overflow-y: auto;
  min-width: 200px;
  max-width: 300px;
  margin: 0;
  gap: var(--spacing-tiny);
  scroll-behavior: smooth;
}

/* ========================================
   CATEGORY-TITLE ELEMENT
   ======================================== */

.category-title {
  font-size: var(--font-size-title-medium);
  font-weight: var(--font-weight-bold);
  color: var(--color-text-primary);
  text-transform: uppercase;
  letter-spacing: 1px;
  margin-bottom: var(--spacing-huge);
  line-height: 1.2;
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

/* ========================================
   TOOLS-CARDS-CONTENT ELEMENT
   ======================================== */

.tools-cards-content {
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  padding: var(--spacing-small);
  overflow-y: auto;
  margin: 0;
  gap: var(--spacing-tiny);
  scroll-behavior: smooth;
}

/* ========================================
   TOOL-CARD ELEMENTS
   ======================================== */

.tool-card {
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  padding: var(--spacing-large);
  overflow-y: auto;
  max-height: var(--card-max-height);
  max-width: 280px;
  margin: 0;
  transition: var(--transition-smooth);
  cursor: pointer;
  border: var(--border-width-thin) solid var(--color-border-primary);
  border-left: 3px solid var(--color-primary-accent);
  position: relative;
  background-color: var(--color-background-card);
  box-shadow: var(--shadow-subtle);
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  -webkit-tap-highlight-color: transparent;
  /* Entrance animation with staggered delay */
  animation: slideInUp var(--animation-duration-medium) var(--animation-ease-smooth) both;
}

.tool-card:nth-child(1) { animation-delay: 0.1s; }
.tool-card:nth-child(2) { animation-delay: 0.2s; }
.tool-card:nth-child(3) { animation-delay: 0.3s; }
.tool-card:nth-child(4) { animation-delay: 0.4s; }
.tool-card:nth-child(5) { animation-delay: 0.5s; }

.tool-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, var(--color-background-hover), transparent);
  opacity: 0;
  transition: var(--transition-fast);
  z-index: -1;
}

.tool-card::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, var(--color-primary-accent), var(--color-primary-accent-light));
  opacity: 0;
  transition: var(--transition-smooth);
  z-index: -2;
}

.tool-card:hover::before {
  opacity: 1;
}

.tool-card:hover::after {
  opacity: 0.05;
}

.tool-card:hover {
  transform: var(--card-hover-transform);
  box-shadow: var(--shadow-strong);
  border-left-color: var(--color-primary-accent-hover);
  border-color: var(--color-primary-accent);
}

.tool-card:focus {
  outline: none;
  border-left-color: var(--color-primary-accent-hover);
}

.tool-card:active {
  transform: var(--card-active-transform);
}

/* ========================================
   MIDDLE-SECTION ELEMENT
   ======================================== */

.middle-section {
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  gap: var(--spacing-medium);
  max-width: 700px;
  margin: 0 auto;
}

/* ========================================
   AUTOSPACE-SECTION ELEMENT
   ======================================== */

.autospace-section {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-large);
}

/* ========================================
   BOX-TITLE ELEMENTS
   ======================================== */

.box-title {
  display: flex;
  align-items: center;
  gap: var(--spacing-small);
  font-size: var(--font-size-title-medium);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
  margin-bottom: var(--spacing-large);
  text-transform: uppercase;
  letter-spacing: 1px;
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

/* ========================================
   DEFAULT-VIEW ELEMENT
   ======================================== */

.default-view {
  padding: var(--spacing-extra-large);
  text-align: center;
  color: var(--color-text-secondary);
  font-style: italic;
  line-height: 1.6;
}

/* ========================================
   SELECTION-VIEW ELEMENT
   ======================================== */

.selection-view {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-large);
  padding: var(--spacing-extra-large);
  position: relative;
  overflow: hidden;
  /* Entrance animation */
  animation: fadeIn var(--animation-duration-medium) var(--animation-ease-smooth) 0.3s both;
}

.selection-view::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, var(--color-background-hover), transparent);
  opacity: 0.5;
  z-index: -1;
}

/* ========================================
   TOOL-NAME ELEMENT
   ======================================== */

.tool-name {
  font-size: var(--font-size-title-medium);
  font-weight: var(--font-weight-bold);
  color: var(--color-text-primary);
  line-height: 1.2;
  margin-bottom: var(--spacing-small);
}

/* ========================================
   TOOL-DESCRIPTION ELEMENT
   ======================================== */

.tool-description {
  color: var(--color-text-secondary);
  line-height: 1.6;
  margin-bottom: var(--spacing-medium);
}

/* ========================================
   TOOL-BUTTONS ELEMENT
   ======================================== */

.tool-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-medium);
}

.tool-buttons button {
  max-width: 100px;
  max-height: 44px;
}

/* ========================================
   RUN-BUTTON ELEMENT
   ======================================== */

.run-button {
  padding: var(--spacing-medium) var(--spacing-large);
  font-weight: var(--font-weight-semibold);
  border: none;
  cursor: pointer;
  transition: var(--transition-smooth);
  font-family: var(--font-family-primary);
  display: flex;
  gap: var(--spacing-small);
  align-items: center;
  justify-content: center;
  max-width: 140px;
  min-height: 48px;
  font-size: var(--font-size-regular);
  line-height: 1;
  background: var(--color-primary-accent);
  color: var(--color-text-on-accent);
  position: relative;
  overflow: hidden;
  z-index: 1;
  box-shadow: var(--shadow-medium);
  letter-spacing: 0.5px;
  text-transform: uppercase;
}

.run-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: var(--transition-smooth);
}

.run-button:hover::before {
  left: 100%;
}

.run-button i {
  font-size: var(--font-size-icon-medium);
  transition: var(--transition-smooth);
  display: inline-block;
}

.run-button:focus {
  outline: none;
  background: var(--color-primary-accent-hover);
  box-shadow: var(--shadow-focus);
}

.run-button:hover {
  transform: var(--button-hover-transform);
  box-shadow: var(--shadow-strong);
  background: var(--color-primary-accent-hover);
}

.run-button:hover i {
  transform: scale(var(--hover-scale-subtle));
}

.run-button:active {
  transform: var(--button-active-transform);
}

.run-button:disabled {
  background: var(--color-text-muted);
  color: var(--color-text-secondary);
  cursor: not-allowed;
  transform: none;
  opacity: 0.6;
}

.run-button:disabled::before {
  display: none;
}

/* ========================================
   FILE-LABEL ELEMENT (Upload Button)
   ======================================== */

.file-label {
  padding: var(--spacing-medium) var(--spacing-large);
  cursor: pointer;
  transition: var(--transition-smooth);
  font-weight: var(--font-weight-semibold);
  background-color: var(--color-background-secondary);
  color: var(--color-text-primary);
  border: var(--border-width-thin) solid var(--color-border-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-small);
  position: relative;
  overflow: hidden;
  font-family: var(--font-family-primary);
  min-width: 120px;
  min-height: 48px;
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  -webkit-tap-highlight-color: transparent;
  box-shadow: var(--shadow-subtle);
  letter-spacing: 0.5px;
  text-transform: uppercase;
}

.file-label::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--color-background-hover);
  opacity: 0;
  transition: var(--transition-fast);
  z-index: -1;
}

.file-label:hover::before {
  opacity: 1;
}

.file-label i {
  font-size: var(--font-size-icon-medium);
  transition: var(--transition-smooth);
  z-index: 1;
}

.file-label:hover {
  background-color: var(--color-primary-accent);
  color: var(--color-text-on-accent);
  transform: var(--button-hover-transform);
  box-shadow: var(--shadow-medium);
  border-color: var(--color-primary-accent);
}

.file-label:hover i {
  transform: scale(var(--hover-scale-subtle));
}

.file-label:focus {
  border-color: var(--color-primary-accent);
  box-shadow: var(--shadow-focus);
}

.file-label:active {
  transform: var(--button-active-transform);
}

/* ========================================
   INPUT[TYPE="FILE"] ELEMENT
   ======================================== */

input[type="file"] {
  display: none;
}

/* ========================================
   DOWNLOAD-SAMPLE-BUTTON ELEMENT
   ======================================== */

.download-sample-button {
  padding: var(--spacing-medium) var(--spacing-large);
  font-weight: var(--font-weight-semibold);
  cursor: pointer;
  transition: var(--transition-smooth);
  border: var(--border-width-thin) solid var(--color-border-primary);
  outline: none;
  position: relative;
  overflow: hidden;
  font-family: var(--font-family-primary);
  display: flex;
  gap: var(--spacing-small);
  align-items: center;
  justify-content: center;
  max-width: 220px;
  min-height: 48px;
  min-width: 220px;
  font-size: var(--font-size-regular);
  line-height: 1;
  background-color: var(--color-background-secondary);
  color: var(--color-text-primary);
  z-index: 1;
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  -webkit-tap-highlight-color: transparent;
  box-shadow: var(--shadow-subtle);
  letter-spacing: 0.5px;
  text-transform: uppercase;
}

.download-sample-button i {
  font-size: var(--font-size-icon-medium);
  transition: var(--transition-smooth);
  display: inline-block;
}

.download-sample-button:focus {
  border-color: var(--color-primary-accent);
  box-shadow: var(--shadow-focus);
}

.download-sample-button:hover {
  background-color: var(--color-primary-accent);
  color: var(--color-text-on-accent);
  transform: var(--button-hover-transform);
  box-shadow: var(--shadow-medium);
  border-color: var(--color-primary-accent);
}

.download-sample-button:hover i {
  transform: translateY(var(--spacing-tiny)) scale(var(--hover-scale-subtle));
}

.download-sample-button:active {
  transform: var(--button-active-transform);
}

.download-sample-button:disabled {
  color: var(--color-text-muted);
  border-left-color: var(--color-text-muted);
  cursor: not-allowed;
  transform: none;
}

/* ========================================
   CHECKBOX-CONTAINER ELEMENT
   ======================================== */

.checkbox-container {
  display: flex;
  align-items: center;
  gap: var(--spacing-small);
  cursor: pointer;
  padding: var(--spacing-small) var(--spacing-medium);
  transition: var(--transition-smooth);
  border-right: var(--border-width-medium) solid transparent;
  position: relative;
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  -webkit-tap-highlight-color: transparent;
}

.checkbox-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--color-background-hover);
  opacity: 0;
  transition: var(--transition-fast);
  z-index: -1;
}

.checkbox-container:hover::before {
  opacity: 1;
}

.checkbox-container:hover {
  border-right-color: var(--color-primary-accent);
  background-color: var(--color-background-card);
}

/* ========================================
   INPUT[TYPE="CHECKBOX"] ELEMENT
   ======================================== */

input[type="checkbox"] {
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  width: 20px;
  height: 20px;
  background-color: var(--color-background-card);
  border: var(--border-width-medium) solid var(--color-border-primary);
  cursor: pointer;
  transition: var(--transition-smooth);
  position: relative;
  flex-shrink: 0;
  box-shadow: var(--shadow-subtle);
}

input[type="checkbox"]:hover {
  border-color: var(--color-primary-accent);
  transform: var(--input-hover-transform);
  box-shadow: var(--shadow-medium);
}

input[type="checkbox"]:checked {
  background-color: var(--color-primary-accent);
  border-color: var(--color-primary-accent);
  transform: var(--input-hover-transform);
}

input[type="checkbox"]:checked::after {
  content: '✓';
  position: absolute;
  top: -2px;
  left: 2px;
  color: var(--color-text-inverse);
  font-size: 12px;
  font-weight: var(--font-weight-bold);
  line-height: 1;
}

input[type="checkbox"]:focus {
  border-color: var(--color-primary-accent);
  box-shadow: var(--shadow-focus);
}

input[type="checkbox"]:active {
  transform: var(--input-active-transform);
}

input[type="checkbox"]:disabled {
  border-color: var(--color-text-muted);
  background-color: var(--color-border-secondary);
  cursor: not-allowed;
}

input[type="checkbox"]:disabled:checked {
  background-color: var(--color-text-muted);
}

/* ========================================
   LABEL ELEMENT
   ======================================== */

label {
  font-size: var(--font-size-small);
  color: var(--color-text-primary);
  cursor: pointer;
  font-weight: var(--font-weight-medium);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  transition: var(--transition-smooth);
  line-height: 1.4;
}

.checkbox-container:hover label {
  color: var(--color-primary-accent);
}

label:focus {
  color: var(--color-primary-accent);
}

label:active {
  transform: var(--input-active-transform);
}

/* ========================================
   SESSIONS-SECTION ELEMENT
   ======================================== */

.sessions-section {
  display: flex;
  flex-direction: row;
  padding: var(--spacing-extra-large);
  overflow-y: auto;
  max-height: 400px;
  justify-content: space-between;
  scroll-behavior: smooth;
  gap: var(--spacing-large);
  position: relative;
}

/* ========================================
   SESSIONS-CONTAINER ELEMENT
   ======================================== */

.sessions-container {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-small);
  overflow-y: auto;
  max-height: 380px;
  padding-right: var(--spacing-medium);
  flex: 1;
  position: relative;
}

/* ========================================
   SESSIONS-REFRESH-CONTAINER ELEMENT
   ======================================== */

.sessions-refresh-container {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-small);
  align-items: center;
  justify-content: flex-start;
  padding: var(--spacing-medium);
  min-width: 60px;
  position: relative;
}

/* ========================================
   SESSION-CARD ELEMENTS
   ======================================== */

.session-card {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-small);
  padding: var(--spacing-large);
  padding-top: var(--spacing-extra-large);
  transition: var(--transition-smooth);
  cursor: pointer;
  position: relative;
  background-color: var(--color-background-card);
  box-shadow: var(--shadow-medium);
  margin: var(--spacing-small);
  border: var(--border-width-thin) solid var(--color-border-primary);
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  -webkit-tap-highlight-color: transparent;
}

.session-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--color-background-hover);
  opacity: 0;
  transition: var(--transition-fast);
  z-index: -1;
}

.session-card:hover::before {
  opacity: 1;
}

.session-card:hover {
  transform: var(--card-hover-transform);
  box-shadow: var(--shadow-strong);
  border-color: var(--color-primary-accent);
}

.session-card:focus {
  border-left-color: var(--color-primary-accent);
}

.session-card:active {
  transform: var(--card-active-transform);
}

/* ========================================
   TITLE ELEMENT (inside session-card)
   ======================================== */

.session-card .title {
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
  transition: var(--transition-smooth);
  line-height: 1.3;
}

.session-card:hover .title {
  color: var(--color-primary-accent);
}

/* ========================================
   TIME ELEMENT
   ======================================== */

.time {
  font-size: var(--font-size-small);
  color: var(--color-text-secondary);
  transition: var(--transition-smooth);
}

.session-card:hover .time {
  color: var(--color-text-primary);
}

/* ========================================
   SESSION-BUTTONS ELEMENT
   ======================================== */

.session-card .session-buttons {
  display: flex;
  gap: var(--spacing-small);
  align-items: center;
  flex-wrap: wrap;
}

/* ========================================
   PROGRESS-COUNTER ELEMENT
   ======================================== */

.progress-counter {
  font-weight: var(--font-weight-bold);
  color: var(--color-primary-accent);
  transition: var(--transition-smooth);
}

.session-card:hover .progress-counter {
  color: var(--color-primary-accent-hover);
  transform: scale(var(--hover-scale-subtle));
}

/* ========================================
   REFRESH-BUTTON ELEMENT
   ======================================== */

.refresh-button {
  padding: var(--spacing-medium);
  font-weight: var(--font-weight-semibold);
  cursor: pointer;
  transition: var(--transition-smooth);
  border: var(--border-width-thin) solid var(--color-border-primary);
  outline: none;
  position: static;
  overflow: hidden;
  font-family: var(--font-family-primary);
  display: flex;
  gap: var(--spacing-small);
  align-items: center;
  justify-content: center;
  min-width: 48px;
  min-height: 48px;
  font-size: var(--font-size-regular);
  line-height: 1;
  background-color: var(--color-background-card);
  color: var(--color-text-primary);
  z-index: 2;
  box-shadow: var(--shadow-subtle);
  margin-bottom: var(--spacing-small);
}

.refresh-button i {
  font-size: var(--font-size-icon-large);
  transition: var(--transition-smooth);
  display: inline-block;
}

.refresh-button:focus {
  border-color: var(--color-primary-accent);
}

.refresh-button:hover {
  background-color: var(--color-primary-accent);
  color: var(--color-text-on-accent);
  transform: var(--button-hover-transform);
  box-shadow: var(--shadow-medium);
  border-color: var(--color-primary-accent);
}

.refresh-button:hover i {
  transform: rotate(180deg) scale(var(--hover-scale-subtle));
}

.refresh-button:active {
  transform: var(--button-active-transform);
}

.refresh-button:disabled {
  color: var(--color-text-muted);
  border-color: var(--color-text-muted);
  cursor: not-allowed;
  transform: none;
}

/* ========================================
   KILL-BUTTON ELEMENT
   ======================================== */

.kill-button {
  padding: var(--spacing-tiny);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: var(--transition-smooth);
  border: var(--border-width-thin) solid transparent;
  outline: none;
  position: absolute;
  top: var(--spacing-tiny);
  right: var(--spacing-tiny);
  overflow: hidden;
  font-family: var(--font-family-primary);
  width: 24px;
  height: 24px;
  font-size: var(--font-size-small);
  line-height: 1;
  background-color: var(--color-background-secondary);
  color: var(--color-text-muted);
  z-index: 3;
  display: flex;
  align-items: center;
  justify-content: center;
}

.kill-button i {
  font-size: 12px;
  transition: var(--transition-smooth);
  display: inline-block;
}

.kill-button:focus {
  border-color: var(--color-primary-accent-hover);
  color: var(--color-primary-accent-hover);
}

.kill-button:hover {
  background-color: var(--color-error);
  color: var(--color-text-inverse);
  border-color: var(--color-error);
  transform: scale(var(--hover-scale-subtle));
  box-shadow: var(--shadow-medium);
}

.kill-button:hover::before {
  transform: scale(var(--hover-scale-medium));
}

.kill-button:active {
  transform: var(--button-active-transform);
}

.kill-button:disabled {
  color: var(--color-text-muted);
  border-color: var(--color-text-muted);
  cursor: not-allowed;
  transform: none;
  opacity: 0.5;
}

/* ========================================
   DOWNLOAD-BUTTON ELEMENT
   ======================================== */

.download-button {
  padding: var(--spacing-medium);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: var(--transition-smooth);
  border: none;
  outline: none;
  position: relative;
  overflow: hidden;
  font-family: var(--font-family-primary);
  display: flex;
  gap: var(--spacing-small);
  align-items: center;
  justify-content: center;
  min-width: 44px;
  min-height: 44px;
  font-size: var(--font-size-regular);
  line-height: 1;
  background-color: var(--color-background-card);
  box-shadow: var(--shadow-subtle);
  color: var(--color-text-primary);
  border-bottom: var(--border-width-medium) solid var(--color-text-primary);
  z-index: 1;
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  -webkit-tap-highlight-color: transparent;
}

.download-button i {
  font-size: var(--font-size-icon-medium);
  transition: var(--transition-smooth);
  display: inline-block;
}

.download-button:focus {
  border-bottom-color: var(--color-primary-accent);
}

.download-button:hover {
  background-color: var(--color-text-primary);
  color: var(--color-background-main);
  transform: var(--button-hover-transform);
  box-shadow: var(--card-hover-shadow);
}

.download-button:hover i {
  transform: translateY(var(--spacing-tiny)) scale(var(--hover-scale-subtle));
}

.download-button:active {
  transform: var(--button-active-transform);
}

.download-button:disabled {
  color: var(--color-text-muted);
  border-bottom-color: var(--color-text-muted);
  cursor: not-allowed;
  transform: none;
}


/* ========================================
   RIGHT-SECTION ELEMENT
   ======================================== */

.right-section {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-large);
  min-width: 300px;
}

/* ========================================
   SYSTEM-PERFORMANCE-SECTION ELEMENT
   ======================================== */

.system-performance-section {
  flex-grow: 1;
  padding: var(--spacing-large);
}

/* ========================================
   PERFORMANCE-METRICS ELEMENT
   ======================================== */

.performance-metrics {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: var(--spacing-tiny);
  align-items: start;
}

/* ========================================
   METRIC-CARD ELEMENTS
   ======================================== */

.metric-card {
  max-width: 140px;
  max-height: 140px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  aspect-ratio: 1;
  position: relative;
  border: var(--border-width-thin) solid var(--color-border-primary);
  padding: var(--spacing-large);
  transition: var(--transition-smooth);
  cursor: pointer;
  background-color: var(--color-background-card);
  box-shadow: var(--shadow-medium);
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  -webkit-tap-highlight-color: transparent;
  /* Entrance animation with staggered delay */
  animation: scaleIn var(--animation-duration-medium) var(--animation-ease-bounce) both;
  overflow: hidden;
}

.metric-card:nth-child(1) { animation-delay: 0.1s; }
.metric-card:nth-child(2) { animation-delay: 0.2s; }
.metric-card:nth-child(3) { animation-delay: 0.3s; }
.metric-card:nth-child(4) { animation-delay: 0.4s; }

.metric-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, var(--color-background-hover), transparent);
  opacity: 0;
  transition: var(--transition-fast);
  z-index: -1;
}

.metric-card::after {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(135deg, var(--color-primary-accent), var(--color-primary-accent-light));
  opacity: 0;
  transition: var(--transition-smooth);
  z-index: -2;
}

.metric-card:hover::before {
  opacity: 1;
}

.metric-card:hover::after {
  opacity: 0.1;
}

.metric-card:hover {
  transform: var(--card-hover-transform);
  box-shadow: var(--shadow-strong);
  border-color: var(--color-primary-accent);
}

.metric-card:focus {
  outline: none;
  border-color: var(--color-primary-accent);
}

.metric-card:active {
  transform: var(--card-active-transform);
}

/* ========================================
   METRIC-ICON ELEMENT
   ======================================== */

.metric-icon {
  position: absolute;
  top: var(--spacing-medium);
  right: var(--spacing-medium);
  font-size: var(--font-size-icon-medium);
  color: var(--color-text-secondary);
  transition: var(--transition-smooth);
  z-index: 1;
}

.metric-icon i {
  color: var(--color-text-secondary);
  font-size: var(--font-size-icon-small);
  font-weight: normal;
  transition: var(--transition-smooth);
}

.metric-card:hover .metric-icon i {
  color: var(--color-primary-accent);
  transform: scale(var(--hover-scale-subtle));
}

/* ========================================
   METRIC-CONTENT ELEMENT
   ======================================== */

.metric-content {
  font-weight: var(--font-weight-bold);
  display: flex;
  flex-direction: column;
  gap: var(--spacing-minimal);
  flex-grow: 1;
  z-index: 1;
  position: relative;
}

/* ========================================
   METRIC-LABEL ELEMENT
   ======================================== */

.metric-label {
  font-size: var(--font-size-small);
  font-weight: var(--font-weight-medium);
  color: var(--color-text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  transition: var(--transition-smooth);
  line-height: 1.2;
}

.metric-card:hover .metric-label {
  color: var(--color-text-primary);
}

/* ========================================
   METRIC-VALUE ELEMENT
   ======================================== */

.metric-value {
  font-size: var(--font-size-title-small);
  font-weight: var(--font-weight-bold);
  color: var(--color-text-primary);
  transition: var(--transition-smooth);
  line-height: 1.1;
}

.metric-card:hover .metric-value {
  color: var(--color-primary-accent);
}

.metric-icon i {
  color: var(--color-text-secondary);
  font-size: var(--font-size-icon-small);
  font-weight: normal;
  transition: var(--transition-smooth);
}

.metric-card:hover .metric-icon i {
  color: var(--color-primary-accent);
  transform: scale(var(--hover-scale-subtle));
}

.metric-label {
  font-size: var(--font-size-small);
  font-weight: var(--font-weight-medium);
  color: var(--color-text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  transition: var(--transition-smooth);
  line-height: 1.2;
}

.metric-card:hover .metric-label {
  color: var(--color-text-primary);
}

.metric-value {
  font-size: var(--font-size-title-small);
  font-weight: var(--font-weight-bold);
  color: var(--color-text-primary);
  transition: var(--transition-smooth);
  line-height: 1.1;
}

.metric-card:hover .metric-value {
  color: var(--color-primary-accent);
}

/* ========================================
   ACTIVE-USERS-SECTION ELEMENT
   ======================================== */

.active-users-section {
  flex-grow: 2;
  padding: var(--spacing-large);
}

/* ========================================
   USERS-BOX ELEMENT
   ======================================== */

.users-box {
  gap: var(--spacing-minimal);
}

/* ========================================
   USERS-CONTAINER ELEMENT
   ======================================== */

.users-container {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-minimal);
}

.users-container {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-minimal);
}

/* ========================================
   USER-CARD ELEMENTS
   ======================================== */

.user-card {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-small);
  padding: var(--spacing-large);
  transition: var(--transition-smooth);
  cursor: pointer;
  position: relative;
  background-color: var(--color-background-card);
  box-shadow: var(--shadow-medium);
  border: var(--border-width-thin) solid var(--color-border-primary);
  border-left: var(--border-width-medium) solid var(--color-primary-accent);
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  -webkit-tap-highlight-color: transparent;
}

.user-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--color-background-hover);
  opacity: 0;
  transition: var(--transition-fast);
  z-index: -1;
}

.user-card:hover::before {
  opacity: 1;
}

.user-card:hover {
  transform: var(--card-hover-transform);
  box-shadow: var(--shadow-strong);
  border-color: var(--color-primary-accent);
}

.user-card:focus {
  border-left-color: var(--color-primary-accent);
}

.user-card:active {
  transform: var(--card-active-transform);
}

.user-card:active {
  transform: scale(var(--active-scale));
}

/* ========================================
   USER-HEADER ELEMENT
   ======================================== */

.user-header {
  display: flex;
  align-items: center;
  gap: var(--spacing-small);
}

/* ========================================
   USERNAME ELEMENT
   ======================================== */

.username {
  font-weight: var(--font-weight-medium);
  color: var(--color-text-primary);
  transition: var(--transition-smooth);
}

.user-card:hover .username {
  color: var(--color-primary-accent);
}

/* ========================================
   SESSION-BADGE ELEMENT
   ======================================== */

.session-badge {
  font-size: var(--font-size-small);
  color: var(--color-text-secondary);
  margin-left: auto;
  padding: var(--spacing-tiny) var(--spacing-small);
  background-color: var(--color-background-focus);
  transition: var(--transition-smooth);
}

.user-card:hover .session-badge {
  background-color: var(--color-primary-accent);
  color: var(--color-text-inverse);
}

/* ========================================
   USER-INFO ELEMENT
   ======================================== */

.user-info {
  display: flex;
  justify-content: space-between;
  font-size: var(--font-size-small);
  color: var(--color-text-secondary);
  transition: var(--transition-smooth);
}

.user-card:hover .user-info {
  color: var(--color-text-primary);
}

/* ========================================
   CURRENT-USER ELEMENT
   ======================================== */

.current-user {
  color: var(--color-primary-accent);
  font-weight: var(--font-weight-semibold);
}

/* ========================================
   NO-USERS ELEMENT
   ======================================== */

.no-users {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-small);
  padding: var(--spacing-large);
  color: var(--color-text-secondary);
  text-align: center;
  font-style: italic;
}

/* ========================================
   RESPONSIVE DESIGN & MEDIA QUERIES
   ======================================== */

/* Enhanced Responsive Design with Modern Breakpoints */
@media (max-width: 768px) {
  /* Disable animations on mobile for better performance */
  .tool-card,
  .metric-card,
  .side-bar-container,
  .main-content {
    animation: none;
  }

  .side-bar-container {
    width: var(--sidebar-width-mobile);
    padding: var(--spacing-medium) 0;
    box-shadow: var(--shadow-subtle);
  }

  .tool-category {
    padding: var(--spacing-small);
    font-size: var(--font-size-icon-medium);
  }

  .main-content {
    flex-direction: column;
    padding: var(--spacing-medium);
    gap: var(--spacing-medium);
  }

  .tools-cards-section {
    max-width: none;
    min-width: auto;
  }

  .tool-card {
    max-width: none;
  }

  .performance-metrics {
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-medium);
  }

  .metric-card {
    max-width: none;
  }

  .tool-buttons {
    flex-direction: column;
    width: 100%;
    gap: var(--spacing-small);
  }

  .run-button,
  .download-button,
  .download-sample-button {
    width: 100%;
    max-width: none;
    justify-content: center;
  }

  .refresh-button {
    position: relative;
    top: auto;
    right: auto;
    margin-bottom: var(--spacing-medium);
    width: 100%;
    max-width: none;
    justify-content: center;
  }

  .kill-button {
    position: absolute;
    top: var(--spacing-small);
    right: var(--spacing-small);
    width: 24px;
    height: 24px;
  }

  .selection-view {
    padding: var(--spacing-large);
  }
}

@media (max-width: 480px) {
  .side-bar-container {
    width: var(--sidebar-width-mobile);
  }

  .main-content {
    padding: var(--spacing-small);
  }

  .performance-metrics {
    grid-template-columns: 1fr;
  }
}

/* ========================================
   PRINT STYLES
   ======================================== */

@media print {
  .side-bar-container {
    display: none;
  }

  .main-content {
    padding: 0;
    max-width: none;
  }

  * {
    box-shadow: none !important;
    transform: none !important;
  }
}

/* ========================================
   ACCESSIBILITY & MODERN CSS FEATURES
   ======================================== */

/* High contrast mode support */
@media (prefers-contrast: high) {
  :root {
    --color-primary-accent: #0819f8;
    --color-primary-accent-hover: #b91414;
    --color-text-primary: #000000;
    --color-text-secondary: #333333;
    --color-background-main: #ffffff;
    --color-background-card: #ffffff;
    --color-border-primary: #000000;
    --shadow-subtle: none;
    --shadow-medium: none;
    --shadow-strong: none;
    --color-border-focus: #0819f8;
  }

  .tool-card,
  .metric-card,
  .session-card,
  .user-card {
    border: 2px solid #000000;
  }
}

/* Dark mode support (disabled - maintaining light theme) */
@media (prefers-color-scheme: dark) {
  :root {
    /* Keep light theme colors even in dark mode preference */
    --color-text-primary: #343843;
    --color-text-secondary: #414551;
    --color-text-muted: #6b7280;
    --color-background-main: #d8dee7;
    --color-background-card: #d8dee7;
    --color-background-secondary: #e5e7eb;
    --color-border-primary: #e5e7eb;
    --color-border-secondary: #d1d5db;
  }
}

/* Modern CSS features */
@supports (backdrop-filter: blur(10px)) {
  .glass {
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
  }
}

@supports (container-type: inline-size) {
  .metric-card {
    container-type: inline-size;
  }
}

/* Enhanced focus indicators for better accessibility */
@media (prefers-reduced-motion: no-preference) {
  .focus-ring:focus-visible {
    border-color: var(--color-primary-accent);
    transition: border-color 0.3s ease-in-out;
  }
}

/* Print optimizations */
@media print {
  .side-bar-container {
    display: none;
  }

  .main-content {
    padding: 0;
    max-width: none;
  }

  .tool-card,
  .metric-card,
  .session-card,
  .user-card {
    break-inside: avoid;
    box-shadow: none !important;
    transform: none !important;
    border: 1px solid #000000;
  }

  .animate-fade-in,
  .animate-slide-in-left,
  .animate-slide-in-right,
  .animate-slide-in-up,
  .animate-scale-in {
    animation: none !important;
  }
}

/* ========================================
   SESSIONS CONTAINER HEADERS - STICKY POSITIONING
   ======================================== */

.sessions-container h2{
  position: sticky;
  top: 0;
  background-color: var(--color-background-main);
  z-index: 10;
  margin: 0;
  padding: var(--spacing-medium) 0;
  font-size: var(--font-size-title-medium);
  font-weight: var(--font-weight-bold);
  color: var(--color-text-primary);
  backdrop-filter: blur(8px);
}

