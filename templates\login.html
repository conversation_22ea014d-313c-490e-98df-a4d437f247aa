<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="{{ url_for('static', filename='styles.css') }}">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Material+Symbols+Outlined:opsz,wght,FILL,GRAD@24,200,0,0"/>
    <title>AutoSpace - Sign In</title>
</head>
<body class="auth-body">
    <!-- Skip link for accessibility -->
    <a href="#login-form" class="skip-link">Skip to login form</a>
    
    <div class="auth-container">
        <!-- Background decoration -->
        <div class="auth-background">
            <div class="background-grid"></div>
            <div class="background-overlay"></div>
        </div>

        <!-- Login card -->
        <div class="auth-card elev-3">
            <!-- Header -->
            <div class="auth-header">
                <div class="brand-container">
                    <span class="brand-logo material-symbols-outlined" aria-hidden="true">asterisk</span>
                    <h1 class="brand-title">AutoSpace</h1>
                </div>
                <p class="auth-subtitle">Sign in to your account</p>
                <p class="auth-description">Access the AutoSpace platform with your SiliconExpert credentials</p>
            </div>
            
            <!-- Error/Success Messages -->
            <div class="message-container">
                {% with messages = get_flashed_messages(with_categories=true) %}
                    {% if messages %}
                        {% for category, message in messages %}
                            <div class="alert alert-{{ category }}" role="alert" aria-live="assertive">
                                <span class="material-symbols-outlined" aria-hidden="true">
                                    {% if category == 'error' %}error{% else %}check_circle{% endif %}
                                </span>
                                <span>{{ message }}</span>
                            </div>
                        {% endfor %}
                    {% endif %}
                {% endwith %}
            </div>
            
            <!-- Login Form -->
            <form id="login-form" class="auth-form" method="POST" action="{{ url_for('login') }}" novalidate>
                <div class="form-group">
                    <label for="email" class="form-label">
                        <span class="material-symbols-outlined" aria-hidden="true">email</span>
                        Email Address
                    </label>
                    <input
                        type="email"
                        id="email"
                        name="email"
                        class="form-input"
                        required
                        autocomplete="email"
                        aria-describedby="email-help"
                        placeholder="<EMAIL>"
                        pattern="[a-zA-Z0-9._%+-]+@siliconexpert\.com$"
                    >
                    <div id="email-help" class="form-help">Enter your @siliconexpert.com email address</div>
                </div>
                
                <div class="form-group">
                    <label for="password" class="form-label">
                        <span class="material-symbols-outlined" aria-hidden="true">lock</span>
                        Password
                    </label>
                    <div class="password-input-container">
                        <input
                            type="password"
                            id="password"
                            name="password"
                            class="form-input"
                            required
                            autocomplete="current-password"
                            aria-describedby="password-help"
                            placeholder="Enter your password"
                        >
                        <button
                            type="button"
                            class="password-toggle"
                            aria-label="Toggle password visibility"
                            onclick="togglePassword('password')"
                        >
                            <span class="material-symbols-outlined" id="password-toggle-icon">visibility</span>
                        </button>
                    </div>
                    <div id="password-help" class="form-help">Enter your account password</div>
                </div>

                <button type="submit" class="auth-button primary-button">
                    <span class="material-symbols-outlined" aria-hidden="true">login</span>
                    <span>Sign In</span>
                </button>
            </form>
            
            <!-- Footer -->
            <div class="auth-footer">
                <p class="switch-auth">
                    Don't have an account?
                    <a href="{{ url_for('register') }}" class="auth-link">Create one here</a>
                </p>

                <div class="help-section">
                    <div class="help-item">
                        <span class="material-symbols-outlined" aria-hidden="true">security</span>
                        <span>Secure access for SiliconExpert team members</span>
                    </div>
                    <div class="help-item">
                        <span class="material-symbols-outlined" aria-hidden="true">support</span>
                        <span>Need help? Contact your administrator</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        // Password visibility toggle
        function togglePassword(fieldId) {
            const passwordInput = document.getElementById(fieldId);
            const toggleIcon = document.getElementById(fieldId + '-toggle-icon');

            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                toggleIcon.textContent = 'visibility_off';
            } else {
                passwordInput.type = 'password';
                toggleIcon.textContent = 'visibility';
            }
        }

        // Form validation and enhancement
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.getElementById('login-form');
            const emailInput = document.getElementById('email');
            const passwordInput = document.getElementById('password');

            // Add real-time validation
            emailInput.addEventListener('blur', validateEmail);
            emailInput.addEventListener('input', validateEmailDomain);
            passwordInput.addEventListener('blur', validatePassword);

            // Form submission handling
            form.addEventListener('submit', function(e) {
                if (!validateForm()) {
                    e.preventDefault();
                }
            });

            function validateEmail() {
                const email = emailInput.value.trim();
                const emailRegex = /^[a-zA-Z0-9._%+-]+@siliconexpert\.com$/;

                if (!email) {
                    showFieldError(emailInput, 'Email address is required');
                    return false;
                } else if (!emailRegex.test(email)) {
                    showFieldError(emailInput, 'Must be a valid @siliconexpert.com email address');
                    return false;
                } else {
                    clearFieldError(emailInput);
                    return true;
                }
            }

            function validateEmailDomain() {
                const email = emailInput.value.trim();
                if (email && !email.includes('@siliconexpert.com')) {
                    emailInput.classList.add('warning');
                } else {
                    emailInput.classList.remove('warning');
                }
            }

            function validatePassword() {
                const password = passwordInput.value;
                if (password.length < 1) {
                    showFieldError(passwordInput, 'Password is required');
                    return false;
                } else {
                    clearFieldError(passwordInput);
                    return true;
                }
            }

            function validateForm() {
                const isEmailValid = validateEmail();
                const isPasswordValid = validatePassword();
                return isEmailValid && isPasswordValid;
            }

            function showFieldError(field, message) {
                clearFieldError(field);
                field.classList.add('error');
                const errorDiv = document.createElement('div');
                errorDiv.className = 'field-error';
                errorDiv.textContent = message;
                field.parentNode.appendChild(errorDiv);
            }

            function clearFieldError(field) {
                field.classList.remove('error');
                const existingError = field.parentNode.querySelector('.field-error');
                if (existingError) {
                    existingError.remove();
                }
            }

            // Auto-dismiss alerts after 5 seconds
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(alert => {
                setTimeout(() => {
                    alert.style.opacity = '0';
                    setTimeout(() => alert.remove(), 300);
                }, 5000);
            });
        });
    </script>
</body>
</html>
