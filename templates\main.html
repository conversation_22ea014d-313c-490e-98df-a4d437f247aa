<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="{{ url_for('static', filename='styles.css') }}">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Material+Symbols+Outlined:opsz,wght,FILL,GRAD@24,200,0,0"/>
    <title>AutoSpace</title>
</head>
<body>
    <!-- Skip link for accessibility -->
    <a href="#main-content" class="skip-link">Skip to main content</a>

    <!-- Top Navigation Bar -->
    <header class="topbar" role="banner" aria-label="User navigation">
        <div class="topbar-content">
            <div class="topbar-left">
                <div class="brand-section">
                    <span class="brand-logo material-symbols-outlined" aria-hidden="true">asterisk</span>
                    <h1 class="brand-name">AutoSpace</h1>
                </div>
            </div>

            <div class="topbar-center">
                <div class="search-section">
                    <div class="search-container">
                        <span class="material-symbols-outlined search-icon" aria-hidden="true">search</span>
                        <input type="text" class="search-input" placeholder="Search tools and sessions..." aria-label="Search">
                    </div>
                </div>
            </div>

            <div class="topbar-right">
                <div class="user-profile">
                    <div class="user-info">
                        <div class="user-details">
                            <span class="user-name">{{ current_user.username if current_user else 'Guest' }}</span>
                            <span class="user-email">{{ current_user.email if current_user else '' }}</span>
                        </div>
                        <div class="user-role-badge role-{{ current_user.role if current_user else 'guest' }}">
                            <span class="material-symbols-outlined" aria-hidden="true">
                                {% if current_user and current_user.role == 'admin' %}admin_panel_settings{% else %}person{% endif %}
                            </span>
                            <span>{{ current_user.role.title() if current_user else 'Guest' }}</span>
                        </div>
                    </div>
                    <div class="user-actions">
                        <button class="user-menu-button" aria-label="User menu" aria-expanded="false">
                            <span class="material-symbols-outlined">account_circle</span>
                        </button>
                        <div class="user-menu" role="menu" aria-hidden="true">
                            <a href="#" class="menu-item" role="menuitem">
                                <span class="material-symbols-outlined">settings</span>
                                <span>Settings</span>
                            </a>
                            <a href="#" class="menu-item" role="menuitem">
                                <span class="material-symbols-outlined">help</span>
                                <span>Help</span>
                            </a>
                            <hr class="menu-divider">
                            <a href="{{ url_for('logout') }}" class="menu-item logout" role="menuitem">
                                <span class="material-symbols-outlined">logout</span>
                                <span>Sign Out</span>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- Main application wrapper -->
    <div class="app-wrapper">
        <div class="side-bar-container" role="navigation" aria-label="Tool categories">
            <span class="logo material-symbols-outlined" role="img" aria-label="AutoSpace Logo">
                asterisk
            </span>
            <!-- Enhanced accessibility with proper ARIA labels -->
            <div class="side-bar" role="tablist" aria-orientation="vertical" aria-label="Tool categories">
                <button tabindex="0" class="tool-category material-symbols-outlined comparison" id="1" role="tab"
                        data-label="Compare" aria-label="Compare tools" aria-controls="tools-panel">
                    <span class="material-symbols-outlined">orbit</span>
                    <span class="sr-only">Compare</span>
                </button>
                <button tabindex="0" class="tool-category material-symbols-outlined search" id="2" role="tab"
                        data-label="Search" aria-label="Search tools" aria-controls="tools-panel">
                    <span class="material-symbols-outlined">travel_explore</span>
                    <span class="sr-only">Search</span>
                </button>
                <button tabindex="0" class="tool-category material-symbols-outlined extraction" id="3" role="tab"
                        data-label="Extract" aria-label="Extract tools" aria-controls="tools-panel">
                    <span class="material-symbols-outlined">satellite_alt</span>
                    <span class="sr-only">Extract</span>
                </button>
            </div>
        </div>

        <main class="main-content" id="main-content">
        <section class="tools-cards-section section" role="region" aria-label="Available tools">
            <h3 class="category-title" id="tools-panel" aria-live="polite"></h3>
            <div class="tools-cards-content" role="tabpanel" aria-labelledby="tools-panel">
                <!-- Tool cards will be populated here -->
            </div>
        </section>

        <section class="middle-section section" role="main" aria-label="Tool configuration">
            <div class="autospace-section section">
                <h3 class="box-title">
                    <span class="material-symbols-outlined" aria-hidden="true">rocket_launch</span>
                    AUTOSPACE
                </h3>

                <div class="default-view" role="status" aria-live="polite">
                    <div class="empty-state">
                        <div class="empty-state-icon">
                            <span class="material-symbols-outlined" aria-hidden="true">touch_app</span>
                        </div>
                        <h4>Select a Tool</h4>
                        <p>Choose a tool from the left panel to view its details and configuration options.</p>
                    </div>
                </div>

                <div class="selection-view" role="region" aria-label="Tool configuration panel" aria-live="polite">
                    <div class="tool-header">
                        <h4 class="tool-name" id="selected-tool-name"></h4>
                        <p class="tool-description" id="selected-tool-description"></p>
                    </div>

                    <div class="tool-actions" role="group" aria-label="Tool actions">
                        <!-- Loading indicator -->
                        <div class="loading-indicator" style="display: none;" role="status" aria-label="Processing">
                            <span class="material-symbols-outlined animate-spin">refresh</span>
                            <span>Processing...</span>
                        </div>

                        <!-- Error message container -->
                        <div class="error-message" style="display: none;" role="alert" aria-live="assertive">
                            <span class="material-symbols-outlined">error</span>
                            <span class="error-text"></span>
                        </div>

                        <!-- Success message container -->
                        <div class="success-message" style="display: none;" role="status" aria-live="polite">
                            <span class="material-symbols-outlined">check_circle</span>
                            <span class="success-text"></span>
                        </div>

                        <button class="run-button primary-button" aria-describedby="run-button-help">
                            <span class="material-symbols-outlined" aria-hidden="true">play_arrow</span>
                            <span>Run Tool</span>
                        </button>
                        <div id="run-button-help" class="sr-only">Execute the selected tool with current configuration</div>

                        <label for="fileInput" class="file-label secondary-button">
                            <span class="material-symbols-outlined" aria-hidden="true">upload_file</span>
                            <span>Upload File</span>
                        </label>
                        <input type="file" id="fileInput" accept=".txt" aria-describedby="file-input-help" hidden>
                        <div id="file-input-help" class="sr-only">Upload a text file to process with the selected tool</div>

                        <button class="download-sample-button secondary-button" aria-describedby="download-help">
                            <span class="material-symbols-outlined" aria-hidden="true">download</span>
                            <span>Download Sample</span>
                        </button>
                        <div id="download-help" class="sr-only">Download a sample input file for the selected tool</div>

                        <div class="checkbox-field">
                            <input type="checkbox" id="mode" name="mode" value="fast" aria-describedby="fast-mode-help">
                            <label for="mode">Fast Mode</label>
                        </div>
                        <div id="fast-mode-help" class="sr-only">Enable fast processing mode for quicker results</div>
                    </div>
                </div>

            </div>

            <section class="sessions-section section" role="region" aria-label="Session management">
                <h3 class="section-title">Sessions</h3>
                <div class="sessions-grid">
                    <div class="sessions-column">
                        <div class="sessions-container running-sessions" role="region" aria-label="Active sessions" aria-live="polite">
                            <h4>Active Sessions</h4>
                            <!-- Active sessions will be populated here -->
                        </div>
                    </div>
                    <div class="sessions-column">
                        <div class="sessions-container finished-sessions" role="region" aria-label="Finished sessions">
                            <h4>Finished Sessions</h4>
                            <div class="empty-state">
                                <div class="empty-state-icon">
                                    <span class="material-symbols-outlined" aria-hidden="true">history</span>
                                </div>
                                <h5>No Finished Sessions</h5>
                                <p>Completed sessions will appear here</p>
                            </div>
                        </div>
                    </div>
                    <div class="sessions-actions">
                        <div class="sessions-refresh-container" role="group" aria-label="Session refresh controls">
                            <!-- Refresh buttons will be populated here -->
                        </div>
                    </div>
                </div>
            </section>
        </main>

        <!-- Right Sidebar -->
        <aside class="right-section section" role="complementary" aria-label="System information">
            <!-- System Performance Section -->
            <section class="system-performance-section" role="region" aria-label="System performance metrics">
                <h3 class="box-title">
                    <span class="material-symbols-outlined" aria-hidden="true">monitoring</span>
                    System Performance
                </h3>
                <div class="performance-box box">
                    <div class="performance-metrics grid-2x2" role="group" aria-label="Performance metrics">
                        <div class="metric-card cpu-card" tabindex="0" role="button" aria-label="CPU cores information">
                            <div class="metric-icon">
                                <span class="material-symbols-outlined" aria-hidden="true">memory</span>
                            </div>
                            <div class="metric-content">
                                <span class="metric-label">CPU Cores</span>
                                <span class="metric-value" id="cpu-cores" aria-live="polite">Loading...</span>
                            </div>
                        </div>

                        <div class="metric-card uptime-card" tabindex="0" role="button" aria-label="System uptime information">
                            <div class="metric-icon">
                                <span class="material-symbols-outlined" aria-hidden="true">schedule</span>
                            </div>
                            <div class="metric-content">
                                <span class="metric-label">Uptime</span>
                                <span class="metric-value" id="uptime" aria-live="polite">Loading...</span>
                            </div>
                        </div>

                        <div class="metric-card memory-card" tabindex="0" role="button" aria-label="Memory information">
                            <div class="metric-icon">
                                <span class="material-symbols-outlined" aria-hidden="true">storage</span>
                            </div>
                            <div class="metric-content">
                                <span class="metric-label">Memory</span>
                                <span class="metric-value" id="memory" aria-live="polite">Loading...</span>
                            </div>
                        </div>

                        <div class="metric-card platform-card" tabindex="0" role="button" aria-label="Platform information">
                            <div class="metric-icon">
                                <span class="material-symbols-outlined" aria-hidden="true">computer</span>
                            </div>
                            <div class="metric-content">
                                <span class="metric-label">Platform</span>
                                <span class="metric-value" id="platform" aria-live="polite">Loading...</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- User Profile Section -->
            <section class="user-profile-section" role="region" aria-label="User profile">
                <h3 class="box-title">
                    <span class="material-symbols-outlined" aria-hidden="true">account_circle</span>
                    User Profile
                </h3>
                <div class="profile-box box">
                    <div class="profile-info">
                        {% if current_user %}
                        <div class="profile-details">
                            <div class="profile-name">{{ current_user.full_name }}</div>
                            <div class="profile-username">@{{ current_user.username }}</div>
                            <div class="profile-role">
                                <span class="role-badge role-{{ current_user.role }}">{{ current_user.role.title() }}</span>
                            </div>
                        </div>
                        {% endif %}
                        <div class="profile-actions">
                            <a href="{{ url_for('logout') }}" class="logout-button secondary-button">
                                <span class="material-symbols-outlined" aria-hidden="true">logout</span>
                                <span>Sign Out</span>
                            </a>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Active Users Section -->
            <section class="active-users-section" role="region" aria-label="Active users">
                <h3 class="box-title">
                    <span class="material-symbols-outlined" aria-hidden="true">group</span>
                    Active Users
                    <span class="user-count" id="active-users-count" aria-label="Number of active users">0</span>
                </h3>
                <div class="users-box box">
                    <div class="users-container" id="active-users-list" role="list" aria-live="polite">
                        <div class="empty-state">
                            <div class="empty-state-icon">
                                <span class="material-symbols-outlined" aria-hidden="true">people</span>
                            </div>
                            <h5>Loading Users...</h5>
                            <p>Fetching active user information</p>
                        </div>
                    </div>
                </div>
            </section>
        </aside>
        <!--end of right sidebar-->
    </div>
    <!-- End app-wrapper -->
    
    <script>
        // User menu functionality
        document.addEventListener('DOMContentLoaded', function() {
            const userMenuButton = document.querySelector('.user-menu-button');
            const userMenu = document.querySelector('.user-menu');

            if (userMenuButton && userMenu) {
                userMenuButton.addEventListener('click', function(e) {
                    e.stopPropagation();
                    const isExpanded = userMenuButton.getAttribute('aria-expanded') === 'true';

                    userMenuButton.setAttribute('aria-expanded', !isExpanded);
                    userMenu.setAttribute('aria-hidden', isExpanded);
                });

                // Close menu when clicking outside
                document.addEventListener('click', function() {
                    userMenuButton.setAttribute('aria-expanded', 'false');
                    userMenu.setAttribute('aria-hidden', 'true');
                });

                // Prevent menu from closing when clicking inside it
                userMenu.addEventListener('click', function(e) {
                    e.stopPropagation();
                });
            }
        });

        // Enhanced session loading with error handling and empty states
        async function getAllSessions(){
            try {
                // Show loading state
                showLoadingState();

                // Clear containers to avoid duplications
                runningCardsContainer.innerHTML = '<h4>Active Sessions</h4>'
                finishedCardsContainer.innerHTML = '<h4>Finished Sessions</h4>'
                sessionsRefreshContainer.innerHTML = ''

                // Send request to get all sessions
                const response = await fetch(`/sessions-all/${currentUserId}`)

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const data = await response.json();
                const finished_sessions = data.Done || []
                const running_sessions = data.Running || {}

                // Create cards for running sessions
                const runningSessionKeys = Object.keys(running_sessions);
                if (runningSessionKeys.length > 0) {
                    runningSessionKeys.forEach(session_id => {
                        const sessionData = running_sessions[session_id];
                        const toolName = getToolNameById(sessionData.tool_id);
                        const runCardReload = createSessionCard(session_id, currentUserId, sessionData.progress, sessionData.timestamp, toolName)
                        runningCardsContainer.appendChild(runCardReload)
                    });
                } else {
                    // Add empty state for running sessions
                    const emptyRunning = createEmptyState('No Active Sessions', 'Start a tool to see active sessions here');
                    runningCardsContainer.appendChild(emptyRunning);
                }

                // Create cards for finished sessions
                if (finished_sessions.length > 0) {
                    finished_sessions.forEach((session_data)=> {
                        // session_data format: [session_id, time, tool_name]
                        const doneCardReload = createDoneCard(session_data[0], session_data[1], session_data[2])
                        finishedCardsContainer.appendChild(doneCardReload)
                    });
                } else {
                    // Add empty state for finished sessions
                    const emptyFinished = createEmptyState('No Finished Sessions', 'Completed sessions will appear here');
                    finishedCardsContainer.appendChild(emptyFinished);
                }

                hideLoadingState();
                showSuccessMessage('Sessions loaded successfully');

            } catch (error) {
                console.error('Error loading sessions:', error);
                hideLoadingState();
                showErrorMessage('Failed to load sessions. Please try again.');

                // Add error state to containers
                runningCardsContainer.innerHTML = '<h4>Active Sessions</h4>';
                finishedCardsContainer.innerHTML = '<h4>Finished Sessions</h4>';
                const errorState = createErrorState('Failed to load sessions');
                runningCardsContainer.appendChild(errorState);
            }
        }

        // Helper functions for UI feedback and error handling
        function showLoadingState() {
            const loadingIndicator = document.querySelector('.loading-indicator');
            if (loadingIndicator) {
                loadingIndicator.style.display = 'flex';
            }
        }

        function hideLoadingState() {
            const loadingIndicator = document.querySelector('.loading-indicator');
            if (loadingIndicator) {
                loadingIndicator.style.display = 'none';
            }
        }

        function showErrorMessage(message) {
            const errorContainer = document.querySelector('.error-message');
            const errorText = document.querySelector('.error-text');
            if (errorContainer && errorText) {
                errorText.textContent = message;
                errorContainer.style.display = 'flex';
                setTimeout(() => {
                    errorContainer.style.display = 'none';
                }, 5000);
            }
        }

        function showSuccessMessage(message) {
            const successContainer = document.querySelector('.success-message');
            const successText = document.querySelector('.success-text');
            if (successContainer && successText) {
                successText.textContent = message;
                successContainer.style.display = 'flex';
                setTimeout(() => {
                    successContainer.style.display = 'none';
                }, 3000);
            }
        }

        function createEmptyState(title, description) {
            const emptyState = document.createElement('div');
            emptyState.className = 'empty-state';
            emptyState.innerHTML = `
                <div class="empty-state-icon">
                    <span class="material-symbols-outlined" aria-hidden="true">inbox</span>
                </div>
                <h5>${title}</h5>
                <p>${description}</p>
            `;
            return emptyState;
        }

        function createErrorState(message) {
            const errorState = document.createElement('div');
            errorState.className = 'empty-state error-state';
            errorState.innerHTML = `
                <div class="empty-state-icon">
                    <span class="material-symbols-outlined" aria-hidden="true">error</span>
                </div>
                <h5>Error</h5>
                <p>${message}</p>
                <button onclick="getAllSessions()" class="secondary-button">
                    <span class="material-symbols-outlined" aria-hidden="true">refresh</span>
                    Try Again
                </button>
            `;
            return errorState;
        }


        async function getToolsData(){
            const response = await fetch('/tools-data')
            const data = await response.json();
            toolsData = data
            categoryList.forEach((category)=>{
            category.addEventListener('click', ()=>{
                categoryTitle.innerHTML = `<h2>${category.classList[2].toLocaleUpperCase()}</h2>`
                toolCardsSection.innerHTML = ''
                Object.keys(toolsData).forEach(tool_id => {
                    if (toolsData[tool_id][0].Category_ID.toString() == category.id){
                        toolCard = createToolCard(toolsData[tool_id], tool_id)
                        toolCardsSection.appendChild(toolCard)
                    }
                });
            })
        })
        }
        // Enhanced tool execution with better error handling
        async function runTool(session_id){
            try {
                if (inputFile == null){
                    showErrorMessage("Please upload a file before running the tool");
                    return false;
                }

                if (!currentToolID) {
                    showErrorMessage("Please select a tool before running");
                    return false;
                }

                showLoadingState();

                const formData = new FormData();
                const modeCheckbox = document.getElementById('mode');
                formData.append('mode', modeCheckbox.checked ? 'fast' : 'normal');
                formData.append('file', inputFile);
                formData.append('tool_id', currentToolID);

                // Send request to run the tool and get the progress back
                const response = await fetch(`/session-run/${session_id}-${currentUserId}`, {
                    method: 'POST',
                    body: formData
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const data = await response.json();

                // Clear file input
                inputFile = null;
                fileInput.value = "";

                hideLoadingState();
                showSuccessMessage("Tool started successfully");
                return true;

            } catch (error) {
                console.error('Error running tool:', error);
                hideLoadingState();
                showErrorMessage("Failed to start tool. Please try again.");
                return false;
            }
        }

        function createToolCard(toolCardData, toolId){
            const toolCard = document.createElement('div')
            toolCard.className = `tool-card`
            toolCard.id = toolId
            toolCard.classList.add(toolCardData[0].Tool_Name)
            toolCard.innerHTML = `<h3>${toolCardData[0].Name}</h3>`
            toolCard.addEventListener('click', ()=>{
                toolDescrtiption.innerHTML = toolCardData[0].Description
                toolName.innerHTML = toolCardData[0].Name
                spaceDefaultView.style.display = "none"
                spaceSelectionView.style.display = "flex"

                currentToolID = toolId
            })
            
            return toolCard
        }

        function createSessionCard(currentSessionId, currentUserId, progress, currentTime, toolName = null){
            const currentRunCard = document.createElement('div')
            currentRunCard.className = `session-${currentSessionId} session-card`
            currentRunCard.id = currentSessionId
            const displayName = toolName || `Session #${currentSessionId}`;
            currentRunCard.setAttribute('role', 'article');
            currentRunCard.setAttribute('aria-label', `${displayName}`);
            currentRunCard.innerHTML = `<div class="title">${displayName}</div>`

            const progressCounter = create_progress_counter(progress)
            const refresh_button = create_refresh_button(currentSessionId, progressCounter)
            const kill_button = create_kill_button(currentSessionId)

            const timeCard = create_time_label(currentTime)

            // Add kill button directly to the session card
            currentRunCard.appendChild(kill_button)
            currentRunCard.appendChild(timeCard)
            currentRunCard.appendChild(progressCounter)

            // Add refresh button to the sessions-refresh-container
            sessionsRefreshContainer.appendChild(refresh_button)

            refresh_button.addEventListener('click', async () => {
                try {
                    const response = await fetch(`/session-refresh/${currentSessionId}-${currentUserId}`)
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    const data = await response.json()
                    progressCounter.textContent = `${data.progress}%`
                    progressCounter.setAttribute('aria-label', `Progress: ${data.progress} percent`);

                    if (data.status == "Done"){
                        move_card_to_done(currentRunCard, data.status)
                    }
                } catch (error) {
                    console.error('Error refreshing session:', error);
                    showErrorMessage('Failed to refresh session status');
                }
            })

            kill_button.addEventListener('click', async () => {
                try {
                    if (confirm(`Are you sure you want to kill session ${currentSessionId}?`)) {
                        const response = await fetch(`/session-kill/${currentSessionId}-${currentUserId}`)
                        if (!response.ok) {
                            throw new Error(`HTTP error! status: ${response.status}`);
                        }
                        const data = await response.json()
                        move_card_to_done(currentRunCard, "Killed")
                        showSuccessMessage(`Session ${currentSessionId} terminated`);
                    }
                } catch (error) {
                    console.error('Error killing session:', error);
                    showErrorMessage('Failed to terminate session');
                }
            })

            return currentRunCard
        }


        function createDoneCard(currentSessionId, currentTime, toolName = null){
            const currentDoneCard = document.createElement('div')
            currentDoneCard.className = `done-session-${currentSessionId} session-card`
            const displayName = toolName || `Session #${currentSessionId}`;
            currentDoneCard.innerHTML = `<div class="title">${displayName}</div>`
            const time = create_time_label(currentTime)
            currentDoneCard.appendChild(time)
            const download_button = create_download_button(currentSessionId)
            currentDoneCard.appendChild(download_button)
            return currentDoneCard
        }

        function create_refresh_button(session_id, porgressCounter){
            // creating refresh button that tracks the session progress
            const currentRefreshButton = document.createElement('button')
            currentRefreshButton.className = `refresh-button`
            currentRefreshButton.dataset.sessionId = `${session_id}`
            currentRefreshButton.innerHTML = porgressCounter.innerHTML
            return currentRefreshButton
        }

        function create_kill_button(session_id){
            // creating kill button that kills the session
            const currentKillButton = document.createElement('button')
            currentKillButton.className = `kill-button`
            currentKillButton.dataset.sessionId = `${session_id}`
            currentKillButton.innerHTML = '<i class="material-symbols-outlined">close</i>'
            return currentKillButton
        }

        function create_progress_counter(progress){            
            const progressCounter = document.createElement('span')
            progressCounter.className = `progress-counter`
            progressCounter.innerHTML = `${progress}%`            
            return progressCounter
        }


        function move_card_to_done(currentRunCard, status){

            // Remove corresponding refresh button from sessions-refresh-container
            const sessionId = currentRunCard.id;
            const refreshButton = sessionsRefreshContainer.querySelector(`[data-session-id="${sessionId}"]`);
            if (refreshButton) {
                sessionsRefreshContainer.removeChild(refreshButton);
            }

            if (status == "Killed"){
                if (currentRunCard.parentElement === runningCardsContainer){
                    runningCardsContainer.removeChild(currentRunCard)
                    return
                }

            }

            const buttonContainer = currentRunCard.querySelector('.session-buttons')
            const progressCounter = currentRunCard.querySelector('.progress-counter')

            const time = create_time_label()
            currentRunCard.appendChild(time)

            progressCounter.textContent = `${status}`
            currentRunCard.classList.add('done-session')


            const download_button = create_download_button(currentRunCard.id)

            // Replace button container with download button
            if (buttonContainer) {
                currentRunCard.removeChild(buttonContainer)
            }
            currentRunCard.appendChild(download_button)

            finishedCardsContainer.appendChild(currentRunCard)
        }

        function create_download_button(session_id){
            const download_button = document.createElement('button');
            download_button.className = `download-button`;
            download_button.innerHTML = '<i class="material-symbols-outlined">arrow_downward</i> DOWNLOAD';

            download_button.addEventListener('click', async ()=>{
                const response = await fetch(`/session-download/${session_id}-${currentUserId}`);
                if (!response.ok) { alert("Download failed."); return; }
                const blob = await response.blob();
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `output_${session_id}_${currentUserId}.txt`; // nice filename
                document.body.appendChild(a);
                a.click();
                a.remove();
                URL.revokeObjectURL(url);
            });
            return download_button;
        }


        function create_time_label(currentTime){

            const time = document.createElement('span')

            time.className = `time`
            if (currentTime == null || currentTime == undefined){
                time.innerHTML = getCurrentTimeHHMM()                
            }
            else{
                time.innerHTML = currentTime                
            }
            return time
        }

        function getCurrentTimeHHMM() {
            const now = new Date();
            const hours = String(now.getHours()).padStart(2, "0");   // ensure 2 digits
            const minutes = String(now.getMinutes()).padStart(2, "0"); // ensure 2 digits
            return `${hours}:${minutes}`;
        }

        function makeSessionId() {
            // Example: "S812345-1427" (random + HHMM)
            const rand = Math.floor(100000 + Math.random() * 900000);
            const now = getCurrentTimeHHMM();          // "14:27"
            const hhmm = now.replace(/:/g, "");        // "1427"
            return `S${rand}-${hhmm}`;
        }

        // Helper function to get tool name by ID
        function getToolNameById(toolId) {
            if (toolsData && toolsData[toolId] && toolsData[toolId][0]) {
                return toolsData[toolId][0].Name || `Tool ${toolId}`;
            }
            return `Tool ${toolId}`;
        }

        // declaring the elements we will use
        let currentUserId = "{{ current_user.user_id if current_user else 'GUEST' }}"
        let toolsData = {}; // Global tools data storage
        const runButton = document.querySelector(".run-button")
        const runningCardsContainer= document.querySelector(".running-sessions")
        const finishedCardsContainer = document.querySelector(".finished-sessions")
        const sessionsRefreshContainer = document.querySelector(".sessions-refresh-container")
        // const userList = document.querySelectorAll(".user-selection li")
        const categoryList = document.querySelectorAll(".tool-category")
        const fileInput = document.getElementById('fileInput');
        let inputFile = null
        const toolCardsSection = document.querySelector(".tools-cards-content")
        const categoryTitle = document.querySelector(".category-title")
        let currentToolID = null
        let currentToolDescription = null
        const spaceDefaultView = document.querySelector(".default-view")
        const spaceSelectionView = document.querySelector(".selection-view")
        const toolDescrtiption = document.querySelector(".tool-description")
        const toolName = document.querySelector(".tool-name")
        
        spaceSelectionView.style.display = "none"

        document.body.style.zoom = "125%"
        getToolsData()
        // load all sessions [finished and running] to see realtime update
        getAllSessions()
        //section added by AI
        // Initialize system performance monitoring
        initializeSystemPerformance()
        // Initialize active users monitoring
        initializeActiveUsers()
        // End of Section Added by AI

        fileInput.addEventListener('change', (event)=> {
            inputFile = event.target.files[0]
        });
       

        // adding the functionality for the runButton
        runButton.addEventListener('click', async ()=>{
            // creating a random number for session id
            const currentTime = getCurrentTimeHHMM()
            session_id = makeSessionId()
            // before creating the card we send request to run tools and make sure we get the response to create the card
            const session_status  = await runTool(session_id)

            if (session_status){
            // creating the session card and assigning the session id
            const toolName = getToolNameById(currentToolID);
            const runCard = createSessionCard(session_id, currentUserId, 0, currentTime, toolName)

            runningCardsContainer.appendChild(runCard)}

        })

        // Section Added by AI
        // System Performance Monitoring
        function initializeSystemPerformance() {
            // Get CPU cores
            const cpuCores = navigator.hardwareConcurrency || 'Unknown';
            document.getElementById('cpu-cores').textContent = cpuCores;

            // Get memory (if available)
            const memory = navigator.deviceMemory ? `${navigator.deviceMemory} GB` : 'Unknown';
            document.getElementById('memory').textContent = memory;

            // Get platform
            const platform = navigator.platform || 'Unknown';
            document.getElementById('platform').textContent = platform;

            // Initialize uptime counter
            const startTime = Date.now();
            updateUptime(startTime);

            // Update uptime every second
            setInterval(() => updateUptime(startTime), 1000);
        }

        function updateUptime(startTime) {
            const uptime = Math.floor((Date.now() - startTime) / 1000);
            const hours = Math.floor(uptime / 3600);
            const minutes = Math.floor((uptime % 3600) / 60);
            const seconds = uptime % 60;

            let uptimeText = '';
            if (hours > 0) {
                uptimeText = `${hours}h ${minutes}m ${seconds}s`;
            } else if (minutes > 0) {
                uptimeText = `${minutes}m ${seconds}s`;
            } else {
                uptimeText = `${seconds}s`;
            }

            document.getElementById('uptime').textContent = uptimeText;
        }

        // Active Users Monitoring
        function initializeActiveUsers() {
            // Simulate active users data
            loadActiveUsers();

            // Update active users every 30 seconds
            setInterval(loadActiveUsers, 30000);
        }

        function loadActiveUsers() {
            // Simulate active users data (replace with actual API call)
            const mockUsers = [
                {
                    username: 'Current User',
                    isCurrentUser: true,
                    lastActivity: 'Just now',
                    activityType: 'Active',
                    sessionCount: 2
                },
                {
                    username: 'User_001',
                    isCurrentUser: false,
                    lastActivity: '2 min ago',
                    activityType: 'Processing',
                    sessionCount: 1
                },
                {
                    username: 'User_002',
                    isCurrentUser: false,
                    lastActivity: '5 min ago',
                    activityType: 'Idle',
                    sessionCount: 0
                }
            ];

            displayActiveUsers(mockUsers);
        }

        function displayActiveUsers(users) {
            const activeUsersContainer = document.getElementById('active-users-list');
            const activeUsersCount = document.getElementById('active-users-count');

            if (users.length === 0) {
                activeUsersContainer.innerHTML = `
                    <div class="no-users">
                        <i class="material-symbols-outlined">info</i>
                        <p>No active users</p>
                    </div>`;
                activeUsersCount.textContent = '0';
                return;
            }

            const userCards = users.map(user => createUserCard(user)).join('');
            activeUsersContainer.innerHTML = userCards;
            activeUsersCount.textContent = users.length;
        }

        function createUserCard(user) {
            const userIcon = user.isCurrentUser ?
                '<i class="material-symbols-outlined">account_circle</i>' :
                '<i class="material-symbols-outlined">person</i>';

            const sessionBadge = user.sessionCount > 0 ?
                `<span class="session-badge">${user.sessionCount} sessions</span>` : '';

            const currentUserClass = user.isCurrentUser ? 'current-user' : '';

            return `
                <div class="user-card ${currentUserClass}">
                    <div class="user-header">
                        ${userIcon}
                        <span class="username">${user.username}${user.isCurrentUser ? ' (You)' : ''}</span>
                        ${sessionBadge}
                    </div>
                    <div class="user-info">
                        <span class="last-activity">${user.lastActivity}</span>
                        <span class="activity-type">${user.activityType}</span>
                    </div>
                </div>`;
        }
        // End of Section Added by AI
    </script>
</body>
</html>