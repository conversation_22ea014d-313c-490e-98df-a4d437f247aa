<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="{{ url_for('static', filename='styles.css') }}">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Material+Symbols+Outlined:opsz,wght,FILL,GRAD@24,200,0,0"/>
    <title>AutoSpace - Create Account</title>
</head>
<body class="auth-body">
    <!-- Skip link for accessibility -->
    <a href="#register-form" class="skip-link">Skip to registration form</a>
    
    <div class="auth-container">
        <!-- Background decoration -->
        <div class="auth-background">
            <div class="background-grid"></div>
            <div class="background-overlay"></div>
        </div>
        
        <!-- Registration card -->
        <div class="auth-card elev-3">
            <!-- Header -->
            <div class="auth-header">
                <div class="brand-container">
                    <span class="brand-logo material-symbols-outlined" aria-hidden="true">asterisk</span>
                    <h1 class="brand-title">AutoSpace</h1>
                </div>
                <p class="auth-subtitle">Create your account</p>
                <p class="auth-description">Join the AutoSpace platform with your SiliconExpert email</p>
            </div>
            
            <!-- Error/Success Messages -->
            <div class="message-container">
                {% with messages = get_flashed_messages(with_categories=true) %}
                    {% if messages %}
                        {% for category, message in messages %}
                            <div class="alert alert-{{ category }}" role="alert" aria-live="assertive">
                                <span class="material-symbols-outlined" aria-hidden="true">
                                    {% if category == 'error' %}error{% else %}check_circle{% endif %}
                                </span>
                                <span>{{ message }}</span>
                            </div>
                        {% endfor %}
                    {% endif %}
                {% endwith %}
            </div>
            
            <!-- Registration Form -->
            <form id="register-form" class="auth-form" method="POST" action="{{ url_for('register') }}" novalidate>
                <div class="form-group">
                    <label for="email" class="form-label">
                        <span class="material-symbols-outlined" aria-hidden="true">email</span>
                        Email Address
                    </label>
                    <input 
                        type="email" 
                        id="email" 
                        name="email" 
                        class="form-input" 
                        required 
                        autocomplete="email"
                        aria-describedby="email-help"
                        placeholder="<EMAIL>"
                        pattern="[a-zA-Z0-9._%+-]+@siliconexpert\.com$"
                    >
                    <div id="email-help" class="form-help">Must be a valid @siliconexpert.com email address</div>
                </div>
                
                <div class="form-group">
                    <label for="username" class="form-label">
                        <span class="material-symbols-outlined" aria-hidden="true">person</span>
                        Username
                    </label>
                    <input 
                        type="text" 
                        id="username" 
                        name="username" 
                        class="form-input" 
                        required 
                        autocomplete="username"
                        aria-describedby="username-help"
                        placeholder="Choose a unique username"
                        pattern="[a-zA-Z0-9._-]{3,20}"
                    >
                    <div id="username-help" class="form-help">3-20 characters, letters, numbers, dots, dashes, underscores only</div>
                </div>
                
                <div class="form-group">
                    <label for="password" class="form-label">
                        <span class="material-symbols-outlined" aria-hidden="true">lock</span>
                        Password
                    </label>
                    <div class="password-input-container">
                        <input 
                            type="password" 
                            id="password" 
                            name="password" 
                            class="form-input" 
                            required 
                            autocomplete="new-password"
                            aria-describedby="password-help"
                            placeholder="Create a strong password"
                            minlength="8"
                        >
                        <button 
                            type="button" 
                            class="password-toggle" 
                            aria-label="Toggle password visibility"
                            onclick="togglePassword('password')"
                        >
                            <span class="material-symbols-outlined" id="password-toggle-icon">visibility</span>
                        </button>
                    </div>
                    <div id="password-help" class="form-help">At least 8 characters with letters, numbers, and symbols</div>
                </div>
                
                <div class="form-group">
                    <label for="confirm_password" class="form-label">
                        <span class="material-symbols-outlined" aria-hidden="true">lock_reset</span>
                        Confirm Password
                    </label>
                    <div class="password-input-container">
                        <input 
                            type="password" 
                            id="confirm_password" 
                            name="confirm_password" 
                            class="form-input" 
                            required 
                            autocomplete="new-password"
                            aria-describedby="confirm-password-help"
                            placeholder="Confirm your password"
                        >
                        <button 
                            type="button" 
                            class="password-toggle" 
                            aria-label="Toggle password visibility"
                            onclick="togglePassword('confirm_password')"
                        >
                            <span class="material-symbols-outlined" id="confirm-password-toggle-icon">visibility</span>
                        </button>
                    </div>
                    <div id="confirm-password-help" class="form-help">Must match your password exactly</div>
                </div>
                
                <div class="form-group">
                    <div class="checkbox-field">
                        <input type="checkbox" id="terms" name="terms" required>
                        <label for="terms">I agree to the Terms of Service and Privacy Policy</label>
                    </div>
                </div>
                
                <button type="submit" class="auth-button primary-button">
                    <span class="material-symbols-outlined" aria-hidden="true">person_add</span>
                    <span>Create Account</span>
                </button>
            </form>
            
            <!-- Footer -->
            <div class="auth-footer">
                <p class="switch-auth">
                    Already have an account? 
                    <a href="{{ url_for('login') }}" class="auth-link">Sign in here</a>
                </p>
                
                <div class="help-section">
                    <div class="help-item">
                        <span class="material-symbols-outlined" aria-hidden="true">security</span>
                        <span>Your account will be verified against our whitelist</span>
                    </div>
                    <div class="help-item">
                        <span class="material-symbols-outlined" aria-hidden="true">business</span>
                        <span>Only @siliconexpert.com emails are accepted</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        // Password visibility toggle
        function togglePassword(fieldId) {
            const passwordInput = document.getElementById(fieldId);
            const toggleIcon = document.getElementById(fieldId + '-toggle-icon');
            
            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                toggleIcon.textContent = 'visibility_off';
            } else {
                passwordInput.type = 'password';
                toggleIcon.textContent = 'visibility';
            }
        }
        
        // Enhanced form validation
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.getElementById('register-form');
            const emailInput = document.getElementById('email');
            const usernameInput = document.getElementById('username');
            const passwordInput = document.getElementById('password');
            const confirmPasswordInput = document.getElementById('confirm_password');
            const termsCheckbox = document.getElementById('terms');
            
            // Real-time validation
            emailInput.addEventListener('blur', validateEmail);
            emailInput.addEventListener('input', validateEmailDomain);
            usernameInput.addEventListener('blur', validateUsername);
            passwordInput.addEventListener('input', validatePassword);
            confirmPasswordInput.addEventListener('input', validatePasswordMatch);
            
            // Form submission
            form.addEventListener('submit', function(e) {
                if (!validateForm()) {
                    e.preventDefault();
                }
            });
            
            function validateEmail() {
                const email = emailInput.value.trim();
                const emailRegex = /^[a-zA-Z0-9._%+-]+@siliconexpert\.com$/;
                
                if (!email) {
                    showFieldError(emailInput, 'Email address is required');
                    return false;
                } else if (!emailRegex.test(email)) {
                    showFieldError(emailInput, 'Must be a valid @siliconexpert.com email address');
                    return false;
                } else {
                    clearFieldError(emailInput);
                    return true;
                }
            }
            
            function validateEmailDomain() {
                const email = emailInput.value.trim();
                if (email && !email.includes('@siliconexpert.com')) {
                    emailInput.classList.add('warning');
                } else {
                    emailInput.classList.remove('warning');
                }
            }
            
            function validateUsername() {
                const username = usernameInput.value.trim();
                const usernameRegex = /^[a-zA-Z0-9._-]{3,20}$/;
                
                if (!username) {
                    showFieldError(usernameInput, 'Username is required');
                    return false;
                } else if (!usernameRegex.test(username)) {
                    showFieldError(usernameInput, 'Username must be 3-20 characters, letters, numbers, dots, dashes, underscores only');
                    return false;
                } else {
                    clearFieldError(usernameInput);
                    return true;
                }
            }
            
            function validatePassword() {
                const password = passwordInput.value;
                const minLength = password.length >= 8;
                const hasLetter = /[a-zA-Z]/.test(password);
                const hasNumber = /\d/.test(password);
                const hasSymbol = /[!@#$%^&*(),.?":{}|<>]/.test(password);
                
                if (!password) {
                    showFieldError(passwordInput, 'Password is required');
                    return false;
                } else if (!minLength) {
                    showFieldError(passwordInput, 'Password must be at least 8 characters');
                    return false;
                } else if (!hasLetter || !hasNumber) {
                    showFieldError(passwordInput, 'Password must contain letters and numbers');
                    return false;
                } else {
                    clearFieldError(passwordInput);
                    validatePasswordMatch(); // Re-validate confirm password
                    return true;
                }
            }
            
            function validatePasswordMatch() {
                const password = passwordInput.value;
                const confirmPassword = confirmPasswordInput.value;
                
                if (confirmPassword && password !== confirmPassword) {
                    showFieldError(confirmPasswordInput, 'Passwords do not match');
                    return false;
                } else {
                    clearFieldError(confirmPasswordInput);
                    return true;
                }
            }
            
            function validateForm() {
                const isEmailValid = validateEmail();
                const isUsernameValid = validateUsername();
                const isPasswordValid = validatePassword();
                const isPasswordMatchValid = validatePasswordMatch();
                const isTermsAccepted = termsCheckbox.checked;
                
                if (!isTermsAccepted) {
                    showFieldError(termsCheckbox, 'You must agree to the Terms of Service');
                }
                
                return isEmailValid && isUsernameValid && isPasswordValid && isPasswordMatchValid && isTermsAccepted;
            }
            
            function showFieldError(field, message) {
                clearFieldError(field);
                field.classList.add('error');
                const errorDiv = document.createElement('div');
                errorDiv.className = 'field-error';
                errorDiv.textContent = message;
                field.parentNode.appendChild(errorDiv);
            }
            
            function clearFieldError(field) {
                field.classList.remove('error');
                const existingError = field.parentNode.querySelector('.field-error');
                if (existingError) {
                    existingError.remove();
                }
            }
            
            // Auto-dismiss alerts
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(alert => {
                setTimeout(() => {
                    alert.style.opacity = '0';
                    setTimeout(() => alert.remove(), 300);
                }, 5000);
            });
        });
    </script>
</body>
</html>
